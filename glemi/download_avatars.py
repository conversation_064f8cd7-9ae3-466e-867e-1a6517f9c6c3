#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unsplash头像下载脚本
下载与玻璃画艺术主题相关的头像图片
"""

import requests
import os
import time
from urllib.parse import urlencode

# Unsplash API配置
ACCESS_KEY = "sB7G-rHWgjtvcp1Ag78GATMnmDApT_WACkbWdygsgKM"
BASE_URL = "https://api.unsplash.com"

# 玻璃画主题相关关键词
GLASS_ART_KEYWORDS = [
    "glass art artist portrait",
    "creative artist portrait",
    "art studio portrait", 
    "painter artist portrait",
    "glass artist profile",
    "artistic person portrait"
]

# 保存目录
ASSETS_DIR = "assets/avatars"

def create_assets_directory():
    """创建assets目录"""
    if not os.path.exists(ASSETS_DIR):
        os.makedirs(ASSETS_DIR)
        print(f"📁 创建目录: {ASSETS_DIR}")

def search_photos(query, per_page=1):
    """搜索图片"""
    params = {
        'query': query,
        'per_page': per_page,
        'orientation': 'squarish',  # 方形图片适合头像
        'content_filter': 'high',
        'order_by': 'relevant'
    }
    
    headers = {
        'Authorization': f'Client-ID {ACCESS_KEY}'
    }
    
    url = f"{BASE_URL}/search/photos?" + urlencode(params)
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"❌ 搜索图片失败: {e}")
        return None

def download_image(image_url, filename):
    """下载图片"""
    try:
        response = requests.get(image_url)
        response.raise_for_status()
        
        filepath = os.path.join(ASSETS_DIR, filename)
        with open(filepath, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ 下载成功: {filename}")
        return True
    except requests.exceptions.RequestException as e:
        print(f"❌ 下载失败 {filename}: {e}")
        return False

def main():
    """主函数"""
    print("🎨 开始下载Glemi玻璃画主题头像...")
    print(f"🔑 使用Access Key: {ACCESS_KEY[:20]}...")
    
    create_assets_directory()
    
    downloaded_count = 0
    target_count = 6
    
    for i, keyword in enumerate(GLASS_ART_KEYWORDS):
        if downloaded_count >= target_count:
            break
            
        print(f"\n🔍 搜索关键词: '{keyword}'")
        
        # 搜索图片
        result = search_photos(keyword, per_page=1)
        
        if not result or 'results' not in result or len(result['results']) == 0:
            print(f"❌ 没有找到相关图片")
            continue
        
        photo = result['results'][0]
        
        # 获取图片信息
        image_id = photo['id']
        image_url = photo['urls']['regular']  # 使用regular尺寸
        photographer = photo['user']['name']
        
        print(f"📸 找到图片: ID={image_id}, 摄影师={photographer}")
        
        # 下载图片
        filename = f"avatar_{downloaded_count + 1:02d}.jpg"
        
        if download_image(image_url, filename):
            downloaded_count += 1
            print(f"🎯 进度: {downloaded_count}/{target_count}")
        
        # 避免API限制，延迟1秒
        time.sleep(1)
    
    print(f"\n🎉 下载完成! 共下载 {downloaded_count} 张头像")
    print(f"📂 保存位置: {ASSETS_DIR}")
    
    # 生成Flutter assets配置
    print("\n📝 Flutter pubspec.yaml配置:")
    print("  assets:")
    print("    - assets/gl/")
    print("    - assets/avatars/")

if __name__ == "__main__":
    main() 