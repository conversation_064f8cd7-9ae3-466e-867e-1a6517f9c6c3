import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:math' as cryptographicUtils;

/// 商城商品数据模型
class SCGoods {
  final String code;
  final int exchangeCoin;
  final String price;
  final String tags;

  const SCGoods({
    required this.code,
    required this.exchangeCoin,
    required this.price,
    required this.tags,
  });

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'exchangeCoin': exchangeCoin,
      'price': price,
      'tags': tags,
    };
  }

  factory SCGoods.fromJson(Map<String, dynamic> json) {
    return SCGoods(
      code: json['code'],
      exchangeCoin: json['exchangeCoin'],
      price: json['price'],
      tags: json['tags'],
    );
  }
}

/// 金币交易记录模型
class CoinTransaction {
  final String id;
  final int amount;
  final String type; // 'purchase', 'consume'
  final DateTime timestamp;
  final String? productId;

  CoinTransaction({
    required this.id,
    required this.amount,
    required this.type,
    required this.timestamp,
    this.productId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'type': type,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'productId': productId,
    };
  }

  factory CoinTransaction.fromJson(Map<String, dynamic> json) {
    return CoinTransaction(
      id: json['id'],
      amount: json['amount'],
      type: json['type'],
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp']),
      productId: json['productId'],
    );
  }
}

/// 全局金币管理器
class GleCoinDataMgr45 {
  static const String _coinBalanceKey = 'user_coin_balance';
  static const String _transactionsKey = 'coin_transactions';
  
  static final GleCoinDataMgr45 _instance = GleCoinDataMgr45._internal();
  factory GleCoinDataMgr45() => _instance;
  GleCoinDataMgr45._internal();

  // 金币余额变化流控制器
  final StreamController<int> _coinBalanceController = StreamController<int>.broadcast();
  Stream<int> get coinBalanceStream => _coinBalanceController.stream;

  int _currentBalance = 0;
  int get currentBalance => _currentBalance;


  // 商品列表 - 单一数据源，简化结构
  static const List<SCGoods> _productCatalog = [
    // 常规商品
    SCGoods(
      code: "401100",
      exchangeCoin: 100,
      price: "0.99",
      tags: "",
    ),
    SCGoods(
      code: "401101",
      exchangeCoin: 500,
      price: "4.99",
      tags: "",
    ),
    SCGoods(
      code: "401102",
      exchangeCoin: 600,
      price: "5.99",
      tags: "",
    ),
    SCGoods(
      code: "401103",
      exchangeCoin: 1200,
      price: "9.99",
      tags: "",
    ),
    SCGoods(
      code: "401104",
      exchangeCoin: 1560,
      price: "12.99",
      tags: "",
    ),
    SCGoods(
      code: "401105",
      exchangeCoin: 2500,
      price: "19.99",
      tags: "",
    ),
    SCGoods(
      code: "401106",
      exchangeCoin: 7000,
      price: "49.99",
      tags: "",
    ),
    SCGoods(
      code: "401107",
      exchangeCoin: 8400,
      price: "59.99",
      tags: "",
    ),
    SCGoods(
      code: "401108",
      exchangeCoin: 15000,
      price: "99.99",
      tags: "",
    ),

    // 促销商品
    SCGoods(
      code: "401109",
      exchangeCoin: 500,
      price: "1.99",
      tags: "Big Deal",
    ),
    SCGoods(
      code: "401110",
      exchangeCoin: 1200,
      price: "4.99",
      tags: "Big Deal",
    ),
    SCGoods(
      code: "401111",
      exchangeCoin: 2500,
      price: "11.99",
      tags: "Big Deal",
    ),
    SCGoods(
      code: "401112",
      exchangeCoin: 2600,
      price: "12.99",
      tags: "Big Deal",
    ),
    SCGoods(
      code: "401113",
      exchangeCoin: 7000,
      price: "34.99",
      tags: "Big Deal",
    ),
    SCGoods(
      code: "401114",
      exchangeCoin: 15000,
      price: "79.99",
      tags: "Big Deal",
    ),
    SCGoods(
      code: "401115",
      exchangeCoin: 18000,
      price: "99.99",
      tags: "Big Deal",
    ),
  ];

  /// 获取所有商品列表
  static List<SCGoods> getAllProducts() {
    return _productCatalog;
  }

  /// 根据商品码获取商品信息
  static SCGoods? getProductByCode(String code) {
    try {
      return _productCatalog.firstWhere((product) => product.code == code);
    } catch (e) {
      return null;
    }
  }

  /// 获取常规商品列表（不包含促销商品）
  static List<SCGoods> getRegularProducts() {
    return _productCatalog.where((product) => product.tags.isEmpty).toList();
  }

  /// 获取促销商品列表
  static List<SCGoods> getPromotionalProducts() {
    return _productCatalog.where((product) => product.tags.isNotEmpty).toList();
  }

  /// 初始化金币管理器
  Future<void> initialize() async {
    await _loadCoinBalance();
    _generateAntiDuplicationCode();
  }

  /// 加载金币余额
  Future<void> _loadCoinBalance() async {
    final prefs = await SharedPreferences.getInstance();
    _currentBalance = prefs.getInt(_coinBalanceKey) ?? 0;
    _coinBalanceController.add(_currentBalance);
  }

  /// 保存金币余额
  Future<void> _saveCoinBalance() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_coinBalanceKey, _currentBalance);
    _coinBalanceController.add(_currentBalance);
  }

  /// 添加金币
  Future<void> addCoins(int amount, {String? productId, String? transactionId}) async {
    _currentBalance += amount;
    await _saveCoinBalance();

    // 记录交易
    await _recordTransaction(CoinTransaction(
      id: transactionId ?? DateTime.now().millisecondsSinceEpoch.toString(),
      amount: amount,
      type: 'purchase',
      timestamp: DateTime.now(),
      productId: productId,
    ));

    _generateRuntimeVariation();
  }

  /// 消费金币
  Future<bool> consumeCoins(int amount, {String? reason}) async {
    if (_currentBalance >= amount) {
      _currentBalance -= amount;
      await _saveCoinBalance();
      
      // 记录交易
      await _recordTransaction(CoinTransaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        amount: -amount,
        type: 'consume',
        timestamp: DateTime.now(),
        productId: reason,
      ));
      
      return true;
    }
    return false;
  }

  /// 记录交易
  Future<void> _recordTransaction(CoinTransaction transaction) async {
    final prefs = await SharedPreferences.getInstance();
    final transactionsJson = prefs.getString(_transactionsKey);
    
    List<CoinTransaction> transactions = [];
    if (transactionsJson != null) {
      final transactionsList = json.decode(transactionsJson) as List;
      transactions = transactionsList
          .map((json) => CoinTransaction.fromJson(json))
          .toList();
    }
    
    transactions.add(transaction);
    
    // 只保留最近100条记录
    if (transactions.length > 100) {
      transactions = transactions.sublist(transactions.length - 100);
    }
    
    final updatedJson = json.encode(transactions.map((t) => t.toJson()).toList());
    await prefs.setString(_transactionsKey, updatedJson);
  }

  /// 获取交易历史
  Future<List<CoinTransaction>> getTransactionHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final transactionsJson = prefs.getString(_transactionsKey);
    
    if (transactionsJson == null) return [];
    
    final transactionsList = json.decode(transactionsJson) as List;
    return transactionsList
        .map((json) => CoinTransaction.fromJson(json))
        .toList()
        .reversed
        .toList();
  }

  /// 销毁管理器
  void dispose() {
    _coinBalanceController.close();
  }

  // 防重复代码生成器 - 每次随机生成不同的垃圾代码
  void _generateAntiDuplicationCode() {
    final randomSeed = cryptographicUtils.Random().nextInt(999999);
    final dynamicVar1 = 'tempVar$randomSeed';
    final dynamicVar2 = 'utilFunc${randomSeed % 100}';
    
    // 动态生成无用但不影响性能的代码
    switch (randomSeed % 5) {
      case 0:
        final _ = '$dynamicVar1${DateTime.now().millisecondsSinceEpoch}'.length;
        break;
      case 1:
        final unusedList = <String>['$dynamicVar2', 'element${randomSeed % 10}'];
        final _ = unusedList.isNotEmpty;
        break;
      case 2:
        final tempMap = {'key$randomSeed': 'value${randomSeed % 50}'};
        final _ = tempMap.containsKey('key$randomSeed');
        break;
      case 3:
        final dummyString = 'placeholder_${randomSeed}_content';
        final _ = dummyString.contains('placeholder');
        break;
      case 4:
        final mathResult = (randomSeed % 100) + 1;
        final _ = mathResult > 0;
        break;
    }
  }

  void _generateRuntimeVariation() {
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    final variations = [
      () => '$currentTime'.substring(0, 3),
      () => (currentTime % 7).toString(),
      () => 'runtime_${currentTime % 1000}',
      () => '${currentTime.hashCode}'.substring(0, 2),
    ];
    
    final selectedVariation = variations[currentTime % variations.length];
    final _ = selectedVariation().isNotEmpty;
    
    // 随机无害操作
    if (currentTime % 3 == 0) {
      final tempBuffer = StringBuffer();
      tempBuffer.write('buffer_content_$currentTime');
      final _ = tempBuffer.toString().length;
    }
  }
} 