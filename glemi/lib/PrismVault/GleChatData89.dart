import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// 聊天消息数据模型
class ChatMessage {
  final String id;
  final String content;
  final bool isUser;
  final DateTime timestamp;
  final String? coachId;

  ChatMessage({
    required this.id,
    required this.content,
    required this.isUser,
    required this.timestamp,
    this.coachId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'isUser': isUser,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'coachId': coachId,
    };
  }

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'],
      content: json['content'],
      isUser: json['isUser'],
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp']),
      coachId: json['coachId'],
    );
  }
}

/// 聊天会话数据模型
class ChatSession {
  final String coachId;
  final String coachName;
  final String coachImagePath;
  final List<ChatMessage> messages;
  final DateTime lastUpdated;

  ChatSession({
    required this.coachId,
    required this.coachName,
    required this.coachImagePath,
    required this.messages,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() {
    return {
      'coachId': coachId,
      'coachName': coachName,
      'coachImagePath': coachImagePath,
      'messages': messages.map((msg) => msg.toJson()).toList(),
      'lastUpdated': lastUpdated.millisecondsSinceEpoch,
    };
  }

  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      coachId: json['coachId'],
      coachName: json['coachName'],
      coachImagePath: json['coachImagePath'],
      messages: (json['messages'] as List)
          .map((msgJson) => ChatMessage.fromJson(msgJson))
          .toList(),
      lastUpdated: DateTime.fromMillisecondsSinceEpoch(json['lastUpdated']),
    );
  }
}

/// 聊天数据管理器
class GleChatDataManager {
  static const String _chatSessionsKey = 'chat_sessions';
  static const String _freeCountsKey = 'free_chat_counts';
  static const int _defaultFreeCount = 3;

  /// 获取所有聊天会话
  static Future<List<ChatSession>> getAllChatSessions() async {
    final prefs = await SharedPreferences.getInstance();
    final sessionsJson = prefs.getString(_chatSessionsKey);
    
    if (sessionsJson == null) return [];
    
    final sessionsList = json.decode(sessionsJson) as List;
    return sessionsList
        .map((sessionJson) => ChatSession.fromJson(sessionJson))
        .toList();
  }

  /// 获取特定教练的聊天会话
  static Future<ChatSession?> getChatSession(String coachId) async {
    final sessions = await getAllChatSessions();
    try {
      return sessions.firstWhere((session) => session.coachId == coachId);
    } catch (e) {
      return null;
    }
  }

  /// 保存聊天会话
  static Future<void> saveChatSession(ChatSession session) async {
    final prefs = await SharedPreferences.getInstance();
    final sessions = await getAllChatSessions();
    
    // 移除旧会话，添加新会话
    sessions.removeWhere((s) => s.coachId == session.coachId);
    sessions.add(session);
    
    // 按最后更新时间排序
    sessions.sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));
    
    final sessionsJson = json.encode(sessions.map((s) => s.toJson()).toList());
    await prefs.setString(_chatSessionsKey, sessionsJson);
  }

  /// 添加消息到聊天会话
  static Future<void> addMessageToSession({
    required String coachId,
    required String coachName,
    required String coachImagePath,
    required ChatMessage message,
  }) async {
    ChatSession? session = await getChatSession(coachId);
    
    if (session == null) {
      // 创建新会话
      session = ChatSession(
        coachId: coachId,
        coachName: coachName,
        coachImagePath: coachImagePath,
        messages: [message],
        lastUpdated: DateTime.now(),
      );
    } else {
      // 更新现有会话
      session = ChatSession(
        coachId: session.coachId,
        coachName: session.coachName,
        coachImagePath: session.coachImagePath,
        messages: [...session.messages, message],
        lastUpdated: DateTime.now(),
      );
    }
    
    await saveChatSession(session);
  }

  /// 获取教练的免费聊天次数
  static Future<int> getFreeChatCount(String coachId) async {
    final prefs = await SharedPreferences.getInstance();
    final countsJson = prefs.getString(_freeCountsKey);
    
    if (countsJson == null) return _defaultFreeCount;
    
    final counts = json.decode(countsJson) as Map<String, dynamic>;
    return counts[coachId] ?? _defaultFreeCount;
  }

  /// 设置教练的免费聊天次数
  static Future<void> setFreeChatCount(String coachId, int count) async {
    final prefs = await SharedPreferences.getInstance();
    final countsJson = prefs.getString(_freeCountsKey);
    
    Map<String, dynamic> counts = {};
    if (countsJson != null) {
      counts = json.decode(countsJson) as Map<String, dynamic>;
    }
    
    counts[coachId] = count;
    await prefs.setString(_freeCountsKey, json.encode(counts));
  }

  /// 消费一次免费聊天次数
  static Future<bool> consumeFreeChatCount(String coachId) async {
    final currentCount = await getFreeChatCount(coachId);
    if (currentCount > 0) {
      await setFreeChatCount(coachId, currentCount - 1);
      return true;
    }
    return false;
  }

  /// 删除聊天会话
  static Future<void> deleteChatSession(String coachId) async {
    final prefs = await SharedPreferences.getInstance();
    final sessions = await getAllChatSessions();
    
    sessions.removeWhere((session) => session.coachId == coachId);
    
    final sessionsJson = json.encode(sessions.map((s) => s.toJson()).toList());
    await prefs.setString(_chatSessionsKey, sessionsJson);
  }

  /// 清空所有聊天记录
  static Future<void> clearAllChatSessions() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_chatSessionsKey);
  }

  /// 重置所有免费次数
  static Future<void> resetAllFreeCounts() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_freeCountsKey);
  }
} 