import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';
import 'dart:convert';

/// 收藏教练数据管理器
class GleFavoriteDataMgr67 {
  static const String _favoritesKey = 'favorite_coaches';
  
  static final GleFavoriteDataMgr67 _instance = GleFavoriteDataMgr67._internal();
  factory GleFavoriteDataMgr67() => _instance;
  GleFavoriteDataMgr67._internal();

  // 收藏状态变化流控制器
  final StreamController<List<String>> _favoritesController = StreamController<List<String>>.broadcast();
  Stream<List<String>> get favoritesStream => _favoritesController.stream;

  List<String> _currentFavorites = [];
  List<String> get currentFavorites => List.from(_currentFavorites);

  /// 初始化收藏管理器
  Future<void> initialize() async {
    await _loadFavorites();
  }

  /// 加载收藏列表
  Future<void> _loadFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesJson = prefs.getString(_favoritesKey);
    
    if (favoritesJson != null) {
      final favoritesList = json.decode(favoritesJson) as List;
      _currentFavorites = favoritesList.cast<String>();
    } else {
      _currentFavorites = [];
    }
    
    _favoritesController.add(_currentFavorites);
  }

  /// 保存收藏列表
  Future<void> _saveFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesJson = json.encode(_currentFavorites);
    await prefs.setString(_favoritesKey, favoritesJson);
    _favoritesController.add(_currentFavorites);
  }

  /// 添加收藏
  Future<void> addFavorite(String coachId) async {
    if (!_currentFavorites.contains(coachId)) {
      _currentFavorites.add(coachId);
      await _saveFavorites();
      print('🌟 Added to favorites: $coachId');
    }
  }

  /// 移除收藏
  Future<void> removeFavorite(String coachId) async {
    if (_currentFavorites.contains(coachId)) {
      _currentFavorites.remove(coachId);
      await _saveFavorites();
      print('💔 Removed from favorites: $coachId');
    }
  }

  /// 切换收藏状态
  Future<void> toggleFavorite(String coachId) async {
    if (_currentFavorites.contains(coachId)) {
      await removeFavorite(coachId);
    } else {
      await addFavorite(coachId);
    }
  }

  /// 检查是否已收藏
  bool isFavorite(String coachId) {
    return _currentFavorites.contains(coachId);
  }

  /// 获取收藏数量
  int get favoriteCount => _currentFavorites.length;

  /// 清空所有收藏
  Future<void> clearAllFavorites() async {
    _currentFavorites.clear();
    await _saveFavorites();
    print('🗑️ Cleared all favorites');
  }

  /// 销毁管理器
  void dispose() {
    _favoritesController.close();
  }
}
