import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;

/// Glemi艺术背景组件
/// 可重用的艺术渐变背景 + 可选浮动装饰元素 + 背景图片支持
class GleArtisticBg67 extends StatefulWidget {
  final Widget child;
  final bool includeFloatingElements;
  final Color? primaryColor;
  final Color? secondaryColor;
  final Color? accentColor;
  final String? backgroundImage; // 新增背景图片参数
  final String? tabletBackgroundImage; // iPad专用背景图片
  final bool useFixedSize; // 新增固定尺寸参数
  final BoxFit backgroundFit; // 控制背景图片的适配方式

  const GleArtisticBg67({
    super.key,
    required this.child,
    this.includeFloatingElements = true,
    this.primaryColor,
    this.secondaryColor,
    this.accentColor,
    this.backgroundImage, // 可选背景图片
    this.tabletBackgroundImage, // iPad专用背景图片
    this.useFixedSize = false, // 是否使用固定屏幕尺寸
    this.backgroundFit = BoxFit.cover, // 默认使用cover，但可以自定义
  });

  @override
  State<GleArtisticBg67> createState() => _GleArtisticBg67State();
}

class _GleArtisticBg67State extends State<GleArtisticBg67>
    with TickerProviderStateMixin {
  
  late AnimationController _floatCtrl;
  late AnimationController _colorCtrl;
  late Animation<double> _floatAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startAnimations();
  }

  void _initAnimations() {
    _floatCtrl = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );
    
    _colorCtrl = AnimationController(
      duration: const Duration(seconds: 6),
      vsync: this,
    );

    _floatAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _floatCtrl,
      curve: Curves.linear,
    ));

    final defaultPrimary = widget.primaryColor ?? const Color(0xFF87CEEB);
    final defaultAccent = widget.accentColor ?? const Color(0xFFFFF8DC);
    
    _colorAnimation = ColorTween(
      begin: defaultPrimary,
      end: defaultAccent,
    ).animate(CurvedAnimation(
      parent: _colorCtrl,
      curve: Curves.easeInOutSine,
    ));
  }

  void _startAnimations() {
    _floatCtrl.repeat();
    _colorCtrl.repeat(reverse: true);
  }

  /// 根据设备类型选择合适的背景图片
  String? _getBackgroundImage(BuildContext context) {
    if (widget.backgroundImage == null) return null;

    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.shortestSide >= 600; // iPad判断标准

    // 如果是平板且提供了专用背景图片，使用专用图片
    if (isTablet && widget.tabletBackgroundImage != null) {
      return widget.tabletBackgroundImage;
    }

    // 否则使用默认背景图片
    return widget.backgroundImage;
  }

  @override
  void dispose() {
    _floatCtrl.dispose();
    _colorCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        fit: StackFit.expand, // 让Stack填满整个可用空间
        children: [
          // 背景图片层（如果提供）- 直接填满整个屏幕
          if (widget.backgroundImage != null)
            Image.asset(
              widget.backgroundImage!,
              fit: BoxFit.cover, // cover会保持比例并填满，多余部分会被裁剪
              width: double.infinity,
              height: double.infinity,
            ),

          // 渐变叠加层（在背景图片上方）
          AnimatedBuilder(
            animation: _colorAnimation,
            builder: (context, child) {
              return Container(
                width: double.infinity,
                height: double.infinity,
                decoration: _buildArtisticBackground(),
              );
            },
          ),
          
          // 浮动装饰元素（如果启用）
          if (widget.includeFloatingElements)
            AnimatedBuilder(
              animation: _floatAnimation,
              builder: (context, child) {
                return Stack(
                  children: _buildFloatingElements(),
                );
              },
            ),
          
          // 主要内容
          widget.child,
        ],
      ),
    );
  }

  /// 构建艺术化背景装饰
  BoxDecoration _buildArtisticBackground() {
    final primaryColor = widget.primaryColor ?? const Color(0xFF87CEEB);
    final secondaryColor = widget.secondaryColor ?? const Color(0xFFB0E0E6);
    final accentColor = widget.accentColor ?? const Color(0xFFF8F0FF);
    
    // 如果有背景图片，使用更透明的渐变叠加
    final hasBackgroundImage = widget.backgroundImage != null;
    final opacityMultiplier = hasBackgroundImage ? 0.3 : 0.8; // 背景图片时降低不透明度
    
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          (_colorAnimation.value ?? primaryColor).withOpacity(0.8 * opacityMultiplier),
          secondaryColor.withOpacity(0.6 * opacityMultiplier),
          accentColor.withOpacity(0.4 * opacityMultiplier),
        ],
      ),
    );
  }

  /// 构建浮动装饰元素
  List<Widget> _buildFloatingElements() {
    final size = MediaQuery.of(context).size;
    
    return [
      // 左上角大圆
      Positioned(
        left: 50 + 30 * math.sin(_floatAnimation.value),
        top: 80 + 20 * math.cos(_floatAnimation.value * 0.8),
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                Colors.white.withOpacity(0.15),
                Colors.white.withOpacity(0.03),
                Colors.transparent,
              ],
            ),
          ),
        ),
      ),
      
      // 右上角中圆
      Positioned(
        right: 70 + 25 * math.cos(_floatAnimation.value * 1.2),
        top: 120 + 35 * math.sin(_floatAnimation.value * 0.9),
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                const Color(0xFFFFD700).withOpacity(0.12),
                const Color(0xFFFFD700).withOpacity(0.02),
                Colors.transparent,
              ],
            ),
          ),
        ),
      ),
      
      // 中间小圆
      Positioned(
        left: size.width * 0.3 + 40 * math.sin(_floatAnimation.value * 1.5),
        top: size.height * 0.4 + 25 * math.cos(_floatAnimation.value * 1.1),
        child: Container(
          width: 25,
          height: 25,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                const Color(0xFF87CEEB).withOpacity(0.2),
                const Color(0xFF87CEEB).withOpacity(0.05),
                Colors.transparent,
              ],
            ),
          ),
        ),
      ),
      
      // 底部左圆
      Positioned(
        left: 60 + 20 * math.cos(_floatAnimation.value * 1.3),
        bottom: 150 + 30 * math.sin(_floatAnimation.value * 0.7),
        child: Container(
          width: 35,
          height: 35,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                Colors.white.withOpacity(0.1),
                Colors.white.withOpacity(0.02),
                Colors.transparent,
              ],
            ),
          ),
        ),
      ),
      
      // 右下角小圆
      Positioned(
        right: 90 + 15 * math.sin(_floatAnimation.value * 1.4),
        bottom: 200 + 20 * math.cos(_floatAnimation.value * 1.6),
        child: Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                const Color(0xFFDDA0DD).withOpacity(0.15),
                const Color(0xFFDDA0DD).withOpacity(0.03),
                Colors.transparent,
              ],
            ),
          ),
        ),
      ),
    ];
  }
} 