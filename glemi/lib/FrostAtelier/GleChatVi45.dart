import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'dart:math' as math;
import 'package:shared_preferences/shared_preferences.dart';
import '../CulletParts/GleArtisticBg67.dart';
import '../PrismVault/GleChatData89.dart';
import '../PrismVault/GleCoinDataMgr45.dart';
import '../FrostAtelier/GleMainVi78.dart';
import '../FrostAtelier/GleCoinShopVi23.dart';

/// Glemi聊天页面
/// 支持历史记录加载、实时消息同步、免费次数管理
class GleChatVi45 extends StatefulWidget {
  final AiCoach34Model coach;

  const GleChatVi45({
    super.key,
    required this.coach,
  });

  @override
  State<GleChatVi45> createState() => _GleChatVi45State();
}

class _GleChatVi45State extends State<GleChatVi45>
    with TickerProviderStateMixin {
  
  late AnimationController _fadeCtrl;
  late AnimationController _typingCtrl;
  late Animation<double> _fadeAnimation;
  late Animation<double> _typingAnimation;
  
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  List<ChatMessage> _messages = [];
  bool _isTyping = false;
  bool _isSending = false;
  int _freeCount = 3;
  int _coinBalance = 0;
  String _userAvatar = 'assets/avatars/avatar_01.jpg';
  String _userNickname = 'Glass Artist';

  // 金币管理器实例
  final GleCoinDataMgr45 _coinManager = GleCoinDataMgr45();

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _loadUserData();
    _loadChatHistory();
    _loadFreeCount();
    _loadCoinBalance();
    _startAnimations();

    // 监听金币余额变化
    _coinManager.coinBalanceStream.listen((balance) {
      if (mounted) {
        setState(() {
          _coinBalance = balance;
        });
      }
    });
  }

  void _initAnimations() {
    _fadeCtrl = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _typingCtrl = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeCtrl,
      curve: Curves.easeOut,
    ));
    
    _typingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _typingCtrl,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimations() {
    _fadeCtrl.forward();
  }

  /// 加载用户数据
  Future<void> _loadUserData() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _userAvatar = prefs.getString('user_avatar') ?? 'assets/avatars/avatar_01.jpg';
      _userNickname = prefs.getString('user_nickname') ?? 'Glass Artist';
    });
  }

  /// 加载聊天历史记录
  Future<void> _loadChatHistory() async {
    final session = await GleChatDataManager.getChatSession(widget.coach.name);
    if (session != null) {
      setState(() {
        _messages = session.messages;
      });
      _scrollToBottom();
    }
  }

  /// 加载免费次数
  Future<void> _loadFreeCount() async {
    final count = await GleChatDataManager.getFreeChatCount(widget.coach.name);
    setState(() {
      _freeCount = count;
    });
  }

  /// 加载金币余额
  Future<void> _loadCoinBalance() async {
    setState(() {
      _coinBalance = _coinManager.currentBalance;
    });
  }

  /// 检查是否可以发送消息 (免费次数或金币)
  bool _canSendMessage() {
    return _freeCount > 0 || _coinBalance >= 50;
  }

  /// 检查支付方式并返回消息
  String _getPaymentStatus() {
    if (_freeCount > 0) {
      return 'free';
    } else if (_coinBalance >= 50) {
      return 'coin';
    } else {
      return 'insufficient';
    }
  }

  /// 滚动到底部
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// 发送消息
  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _isSending) return;

    // 收起键盘
    FocusScope.of(context).unfocus();

    // 检查支付能力
    final paymentStatus = _getPaymentStatus();
    if (paymentStatus == 'insufficient') {
      _showInsufficientFundsDialog();
      return;
    }

    setState(() {
      _isSending = true;
    });

    // 创建用户消息
    final userMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: message,
      isUser: true,
      timestamp: DateTime.now(),
      coachId: widget.coach.name,
    );

    // 立即添加用户消息到界面并清空输入框
    setState(() {
      _messages.add(userMessage);
      _messageController.clear();
    });
    _scrollToBottom();

    // 保存用户消息到本地
    await GleChatDataManager.addMessageToSession(
      coachId: widget.coach.name,
      coachName: widget.coach.displayName,
      coachImagePath: widget.coach.imagePath,
      message: userMessage,
    );

    // 显示AI正在输入
    setState(() {
      _isTyping = true;
    });
    _typingCtrl.repeat(reverse: true);

    try {
      // 模拟AI回复 (这里应该调用真实的AI API)
      await Future.delayed(const Duration(seconds: 2));
      final aiResponse = _generateAiResponse(message);

      // 创建AI消息
      final aiMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: aiResponse,
        isUser: false,
        timestamp: DateTime.now(),
        coachId: widget.coach.name,
      );

      // 添加AI消息到界面
      setState(() {
        _messages.add(aiMessage);
        _isTyping = false;
      });
      _typingCtrl.stop();
      _scrollToBottom();

      // 保存AI消息到本地
      await GleChatDataManager.addMessageToSession(
        coachId: widget.coach.name,
        coachName: widget.coach.displayName,
        coachImagePath: widget.coach.imagePath,
        message: aiMessage,
      );

      // AI成功回复后才扣除费用
      await _processPayment(paymentStatus);

    } catch (e) {
      // 如果AI回复失败，显示错误信息但不扣除次数
      final errorMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: 'Sorry, I encountered an error. Please try again.',
        isUser: false,
        timestamp: DateTime.now(),
        coachId: widget.coach.name,
      );

      setState(() {
        _messages.add(errorMessage);
        _isTyping = false;
      });
      _typingCtrl.stop();
      _scrollToBottom();

      print('🚨 AI回复错误: $e');
    }

    setState(() {
      _isSending = false;
    });
  }

  /// 处理支付
  Future<void> _processPayment(String paymentStatus) async {
    if (paymentStatus == 'free') {
      // 使用免费次数
      final success = await GleChatDataManager.consumeFreeChatCount(widget.coach.name);
      if (success) {
        final newCount = await GleChatDataManager.getFreeChatCount(widget.coach.name);
        setState(() {
          _freeCount = newCount;
        });
      }
    } else if (paymentStatus == 'coin') {
      // 使用金币
      final success = await _coinManager.consumeCoins(50, reason: 'chat_with_${widget.coach.name}');
      if (success) {
        _showCoinConsumedDialog();
      }
    }
  }

  /// 显示金币消费提示
  void _showCoinConsumedDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              CupertinoIcons.money_dollar_circle_fill,
              color: Colors.amber,
              size: 20,
            ),
            const SizedBox(width: 8),
            const Text(
              'Used 50 coins for this talking',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.amber.withOpacity(0.9),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  /// 显示资金不足对话框
  void _showInsufficientFundsDialog() {
    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.5),
      builder: (context) => BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Dialog(
          backgroundColor: Colors.transparent,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(25),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withOpacity(0.2),
                      Colors.white.withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      CupertinoIcons.exclamationmark_triangle_fill,
                      size: 60,
                      color: Colors.orange,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Insufficient Funds',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'You need 50 coins to continue chatting. Please visit the shop to purchase more coins.',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey.withOpacity(0.3),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                            child: const Text('Cancel'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                              // 导航到金币商城
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const GleCoinShopVi23(),
                                ),
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.amber,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                            child: const Text('Buy Coins'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 生成AI回复 (模拟)
  String _generateAiResponse(String userMessage) {
    final responses = [
      "That's an excellent question about glass painting! Let me guide you through this technique step by step.",
      "I love your enthusiasm for glass art! Here's what I recommend for achieving that effect.",
      "Great observation! This is a common challenge in glass painting, and I have some proven solutions.",
      "Your approach shows real artistic intuition. Let me help you refine this technique further.",
      "That's a wonderful creative idea! Here's how you can implement it in your glass painting practice.",
    ];
    
    return responses[math.Random().nextInt(responses.length)];
  }



  @override
  void dispose() {
    _fadeCtrl.dispose();
    _typingCtrl.dispose();
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildGlassAppBar(),
      body: Stack(
        children: [
          // 固定背景层 - 使用绝对屏幕尺寸
          Positioned(
            top: 0,
            left: 0,
            width: screenSize.width,
            height: screenSize.height,
            child: GleArtisticBg67(
              primaryColor: widget.coach.glassColor,
              secondaryColor: widget.coach.accentColor,
              accentColor: const Color(0xFFFFF8DC),
              includeFloatingElements: false,
              backgroundImage: 'assets/bg2.png', // 添加背景图片
              useFixedSize: true, // 使用固定屏幕尺寸
              child: Container(), // 空容器，只用于背景
            ),
          ),

          // 内容层 - 可以随键盘调整
          SafeArea(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Column(
                children: [
                  // 聊天记录区域
                  Expanded(
                    child: _buildChatArea(),
                  ),

                  // 输入区域
                  _buildInputArea(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建玻璃拟态AppBar
  PreferredSizeWidget _buildGlassAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(60),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: AppBar(
            backgroundColor: Colors.white.withOpacity(0.1),
            elevation: 0,
            leading: IconButton(
              icon: Icon(
                CupertinoIcons.back,
                color: Colors.white.withOpacity(0.9),
                size: 24,
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
            title: Row(
              children: [
                // AI教练头像
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white.withOpacity(0.4),
                      width: 1,
                    ),
                  ),
                  child: ClipOval(
                    child: Image.asset(
                      widget.coach.imagePath,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: widget.coach.glassColor,
                          child: Icon(
                            CupertinoIcons.person_circle,
                            color: Colors.white,
                            size: 20,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        widget.coach.displayName,
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Row(
                        children: [
                          Icon(
                            CupertinoIcons.chat_bubble,
                            size: 12,
                            color: Colors.white.withOpacity(0.7),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Free: $_freeCount',
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.7),
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Icon(
                            CupertinoIcons.money_dollar_circle,
                            size: 12,
                            color: Colors.amber.withOpacity(0.8),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Coins: $_coinBalance',
                            style: TextStyle(
                              color: Colors.amber.withOpacity(0.8),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建聊天区域
  Widget _buildChatArea() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(),
        itemCount: _messages.length + (_isTyping ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _messages.length && _isTyping) {
            return _buildTypingIndicator();
          }
          
          final message = _messages[index];
          return _buildMessageBubble(message);
        },
      ),
    );
  }

  /// 构建消息气泡
  Widget _buildMessageBubble(ChatMessage message) {
    final isUser = message.isUser;
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            // AI头像
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withOpacity(0.4),
                  width: 1,
                ),
              ),
              child: ClipOval(
                child: Image.asset(
                  widget.coach.imagePath,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: widget.coach.glassColor,
                      child: Icon(
                        CupertinoIcons.person_circle,
                        color: Colors.white,
                        size: 24,
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          
          // 消息内容
          Flexible(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: isUser
                        ? [
                            widget.coach.glassColor.withOpacity(0.6),
                            widget.coach.accentColor.withOpacity(0.4),
                          ]
                        : [
                            Colors.white.withOpacity(0.2),
                            Colors.white.withOpacity(0.1),
                          ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    message.content,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      height: 1.4,
                    ),
                  ),
                ),
              ),
            ),
          ),
          
          if (isUser) ...[
            const SizedBox(width: 8),
            // 用户头像
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withOpacity(0.4),
                  width: 1,
                ),
              ),
              child: ClipOval(
                child: Image.asset(
                  _userAvatar,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: const Color(0xFF87CEEB),
                      child: Icon(
                        CupertinoIcons.person_circle,
                        color: Colors.white,
                        size: 24,
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建正在输入指示器
  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // AI头像
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white.withOpacity(0.4),
                width: 1,
              ),
            ),
            child: ClipOval(
              child: Image.asset(
                widget.coach.imagePath,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: widget.coach.glassColor,
                    child: Icon(
                      CupertinoIcons.person_circle,
                      color: Colors.white,
                      size: 24,
                    ),
                  );
                },
              ),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // 正在输入动画
          ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withOpacity(0.2),
                      Colors.white.withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: AnimatedBuilder(
                  animation: _typingAnimation,
                  builder: (context, child) {
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      children: List.generate(3, (index) {
                        final delay = index * 0.2;
                        final opacity = ((math.sin((_typingAnimation.value + delay) * 2 * math.pi) + 1) / 2);
                        
                        return Container(
                          margin: EdgeInsets.only(right: index < 2 ? 4 : 0),
                          child: Opacity(
                            opacity: 0.4 + opacity * 0.6,
                            child: Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        );
                      }),
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建输入区域
  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: Container(
            decoration: BoxDecoration(
              // 背景改为透明
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1.5,
              ),
            ),
            child: Row(
              children: [
                // 输入框
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    decoration: const BoxDecoration(
                      color: Colors.transparent, // 确保Container背景透明
                    ),
                    child: TextField(
                      controller: _messageController,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Ask about glass painting...',
                        hintStyle: TextStyle(
                          color: Colors.white.withOpacity(0.6),
                          fontSize: 16,
                        ),
                        border: InputBorder.none,
                        focusedBorder: InputBorder.none, // 去掉聚焦时的边框
                        enabledBorder: InputBorder.none, // 去掉启用时的边框
                        disabledBorder: InputBorder.none, // 去掉禁用时的边框
                        filled: false, // 确保不填充背景
                        fillColor: Colors.transparent, // 明确设置为透明
                      ),
                      maxLines: null,
                      textInputAction: TextInputAction.send,
                      onSubmitted: (_) {
                        if (_canSendMessage()) {
                          _sendMessage();
                        } else {
                          _showInsufficientFundsDialog();
                        }
                      },
                    ),
                  ),
                ),
                
                // 发送按钮
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: GestureDetector(
                    onTap: () {
                      if (_canSendMessage()) {
                        _sendMessage();
                      } else {
                        _showInsufficientFundsDialog();
                      }
                    },
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            widget.coach.glassColor.withOpacity(0.8),
                            widget.coach.accentColor.withOpacity(0.6),
                          ],
                        ),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white.withOpacity(0.4),
                          width: 1,
                        ),
                      ),
                      child: _isSending
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : Icon(
                            CupertinoIcons.paperplane,
                            color: Colors.white,
                            size: 24,
                          ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 