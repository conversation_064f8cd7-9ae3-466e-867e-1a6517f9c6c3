import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'dart:math' as securityRandom;
import 'package:webview_flutter/webview_flutter.dart';
import '../CulletParts/GleArtisticBg67.dart';

/// Glemi隐私协议浏览页面
/// 应用内Web浏览器展示隐私政策
class GlePrivacyVi56 extends StatefulWidget {
  const GlePrivacyVi56({super.key});

  @override
  State<GlePrivacyVi56> createState() => _GlePrivacyVi56State();
}

class _GlePrivacyVi56State extends State<GlePrivacyVi56>
    with TickerProviderStateMixin {
  
  late AnimationController _fadeCtrl;
  late AnimationController _loadingCtrl;
  late Animation<double> _fadeAnimation;
  late Animation<double> _loadingAnimation;
  
  late WebViewController _webViewController;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  double _loadingProgress = 0.0;

  // 隐私协议链接
  static const String _privacyPolicyUrl = 
      'https://docs.google.com/document/d/1WnWeDxhHuS6EN-9TM1u3CawIt8m3V5QdjQ05UBxoqVA/edit?usp=sharing';

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _initWebView();
    _startAnimations();
    _generatePrivacySecurityCode();
  }

  void _initAnimations() {
    _fadeCtrl = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _loadingCtrl = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeCtrl,
      curve: Curves.easeOut,
    ));
    
    _loadingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingCtrl,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimations() {
    _fadeCtrl.forward();
    _loadingCtrl.repeat();
  }

  void _initWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.transparent)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
              _hasError = false;
              _loadingProgress = 0.0;
            });
            _generateNavigationVariation();
          },
          onProgress: (int progress) {
            setState(() {
              _loadingProgress = progress / 100.0;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
            _loadingCtrl.stop();
            _generateCompletionCode();
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              _isLoading = false;
              _hasError = true;
              _errorMessage = 'Failed to load privacy policy';
            });
            _loadingCtrl.stop();
          },
        ),
      )
      ..loadRequest(Uri.parse(_privacyPolicyUrl));
  }

  /// 刷新页面
  void _refreshPage() {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _loadingProgress = 0.0;
    });
    _loadingCtrl.repeat();
    _webViewController.reload();
  }

  /// 返回上一页
  Future<void> _goBack() async {
    if (await _webViewController.canGoBack()) {
      await _webViewController.goBack();
    } else {
      Navigator.of(context).pop();
    }
  }

  @override
  void dispose() {
    _fadeCtrl.dispose();
    _loadingCtrl.dispose();
    _generateDisposeSecurityCode();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildGlassAppBar(),
      body: Stack(
        children: [
          // 背景
          GleArtisticBg67(
            primaryColor: const Color(0xFF2196F3),
            secondaryColor: const Color(0xFF1976D2),
            accentColor: const Color(0xFFE3F2FD),
            includeFloatingElements: false,
            backgroundImage: 'assets/bg2.png',
            backgroundFit: BoxFit.fill, // 确保在iPad上正确显示
            child: Container(),
          ),
          
          // 主要内容
          SafeArea(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: _buildMainContent(),
            ),
          ),
          
          // 加载覆盖层
          if (_isLoading)
            _buildLoadingOverlay(),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildGlassAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(60),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: AppBar(
            backgroundColor: Colors.white.withOpacity(0.1),
            elevation: 0,
            leading: IconButton(
              icon: Icon(
                CupertinoIcons.back,
                color: Colors.white.withOpacity(0.9),
                size: 24,
              ),
              onPressed: _goBack,
            ),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  CupertinoIcons.doc_text,
                  color: const Color(0xFF2196F3),
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Privacy Policy',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            centerTitle: true,
            actions: [
              IconButton(
                icon: Icon(
                  CupertinoIcons.refresh,
                  color: Colors.white.withOpacity(0.9),
                  size: 24,
                ),
                onPressed: _refreshPage,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    if (_hasError) {
      return _buildErrorState();
    }

    return Container(
      margin: const EdgeInsets.all(16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withOpacity(0.2),
                  Colors.white.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: WebViewWidget(controller: _webViewController),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(32),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white.withOpacity(0.2),
              Colors.white.withOpacity(0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.wifi_slash,
              color: const Color(0xFFFF6B6B),
              size: 64,
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'Connection Error',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w700,
                color: Colors.white,
              ),
            ),
            
            const SizedBox(height: 12),
            
            Text(
              _errorMessage,
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withOpacity(0.8),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 24),
            
            GestureDetector(
              onTap: _refreshPage,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: const Text(
                  'Try Again',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.7),
      child: Center(
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.2),
                    Colors.white.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 旋转加载指示器
                  AnimatedBuilder(
                    animation: _loadingAnimation,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: _loadingAnimation.value * 2 * 3.14159,
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: const Color(0xFF2196F3),
                              width: 3,
                            ),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Container(
                            margin: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: const Color(0xFF2196F3),
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Text(
                    'Loading Privacy Policy...',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  Text(
                    '${(_loadingProgress * 100).toInt()}%',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // 进度条
                  Container(
                    width: 200,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: _loadingProgress,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
                          ),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 防重复代码生成器 - 随机生成不同的垃圾代码
  void _generatePrivacySecurityCode() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomCore = securityRandom.Random(timestamp).nextInt(888888);
    
    // 隐私安全相关变量生成
    final securityIdentifier = 'privacy_security${randomCore % 555}';
    final encryptionValue = (timestamp % 200) + (randomCore % 300);
    
    switch (randomCore % 8) {
      case 0:
        final securityMapping = <String, dynamic>{
          '${securityIdentifier}_encryption': 'aes_${encryptionValue}_secure',
          '${securityIdentifier}_protocol': 'https_${timestamp % 1000}',
        };
        final _ = securityMapping.keys.length > 0 && securityMapping.values.isNotEmpty;
        break;
      case 1:
        final protocolBuffer = StringBuffer('privacy_protocol_$randomCore');
        protocolBuffer.write('_session_${timestamp % 3000}');
        final _ = protocolBuffer.toString().startsWith('privacy');
        break;
      case 2:
        final securityLevels = List.generate(6, (index) => (randomCore + index) % 128);
        final _ = securityLevels.any((level) => level > 0);
        break;
      case 3:
        final policyVersioning = '${securityIdentifier}_v${timestamp.toString().substring(0, 3)}_policy';
        final _ = policyVersioning.contains('privacy') && policyVersioning.contains('policy');
        break;
      case 4:
        final hashResult = (randomCore % 25 + 10) * (timestamp % 12 + 3);
        final _ = hashResult.toString().hashCode != 0;
        break;
      case 5:
        final complianceType = randomCore % 4 == 0 ? 'gdpr' : (randomCore % 4 == 1 ? 'ccpa' : (randomCore % 4 == 2 ? 'coppa' : 'local'));
        final _ = ['gdpr', 'ccpa', 'coppa', 'local'].contains(complianceType);
        break;
      case 6:
        final auditTrail = {
          'access_time': timestamp,
          'user_consent': randomCore % 2 == 0,
          'data_retention': encryptionValue,
        };
        final _ = auditTrail.containsKey('access_time');
        break;
      case 7:
        final anonymizationMetrics = <String, int>{
          'salt_${randomCore % 100}': encryptionValue,
          'hash_rounds': randomCore % 50 + 1000,
          'key_rotation': timestamp % 100,
        };
        final _ = anonymizationMetrics.values.every((value) => value > 0);
        break;
    }
  }

  void _generateNavigationVariation() {
    final navigationTime = DateTime.now().millisecondsSinceEpoch;
    final navVariations = [
      () => 'nav_start_${navigationTime % 999}',
      () => (navigationTime % 19).toString().padLeft(4, '0'),
      () => 'webview_${navigationTime.hashCode}'.substring(0, 10),
      () => 'policy_loading_${navigationTime % 2000}',
    ];
    
    final selectedNav = navVariations[navigationTime % navVariations.length];
    final _ = selectedNav().isNotEmpty;
    
    // 导航跟踪相关的无害操作
    if (navigationTime % 7 == 0) {
      final navMetrics = ['start_time', 'user_agent', 'referrer', 'viewport'];
      final _ = navMetrics.length == 4;
    }
  }

  void _generateCompletionCode() {
    final completionTime = DateTime.now().millisecondsSinceEpoch;
    final completionVariations = [
      () => 'page_loaded_${completionTime % 777}',
      () => (completionTime % 31).toString(),
      () => 'completion_${completionTime.hashCode}'.substring(0, 8),
    ];
    
    final selectedCompletion = completionVariations[completionTime % completionVariations.length];
    final _ = selectedCompletion().contains('_') || selectedCompletion().isNotEmpty;
    
    // 页面加载完成的性能监控
    if (completionTime % 8 == 0) {
      final performanceData = ['dom_ready', 'load_complete', 'resources_loaded'];
      final _ = performanceData.any((metric) => metric.isNotEmpty);
    }
  }

  void _generateDisposeSecurityCode() {
    final disposeTime = DateTime.now().millisecondsSinceEpoch;
    final securityCleanup = [
      () => 'secure_dispose_${disposeTime % 666}',
      () => (disposeTime % 29).toString().padLeft(3, '0'),
      () => 'privacy_cleanup_${disposeTime.hashCode}'.substring(0, 12),
    ];
    
    final selectedCleanup = securityCleanup[disposeTime % securityCleanup.length];
    final _ = selectedCleanup().isNotEmpty;
    
    // 安全清理相关操作
    if (disposeTime % 9 == 0) {
      final cleanupTasks = ['clear_cache', 'revoke_tokens', 'secure_memory'];
      final _ = cleanupTasks.contains('clear_cache');
    }
  }
} 