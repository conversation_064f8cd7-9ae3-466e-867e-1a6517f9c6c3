import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'dart:math' as math;
import '../CulletParts/GleArtisticBg67.dart';
import '../PrismVault/GleFavoriteDataMgr67.dart';
import 'GleCoachDetai88Vi.dart';
import 'GleProfileVi33.dart';
import 'GleChatVi45.dart';

/// Glemi玻璃画创作指导师 - 创新玻璃拟态主页
/// 采用大头像、不对称布局、深度玻璃质感设计
class GleMainVi78 extends StatefulWidget {
  const GleMainVi78({super.key});

  @override
  State<GleMainVi78> createState() => _GleMainVi78State();
}

class _GleMainVi78State extends State<GleMainVi78>
    with TickerProviderStateMixin {
  
  // 动画控制器
  late AnimationController _fadeCtrl;
  late AnimationController _shimmerCtrl;
  late Animation<double> _fadeAnimation;
  late Animation<double> _shimmerAnimation;

  // 收藏管理器
  final GleFavoriteDataMgr67 _favoriteManager = GleFavoriteDataMgr67();
  
  // AI角色数据
  final List<AiCoach34Model> _aiCoaches = [
    AiCoach34Model(
      id: 1,
      name: 'BasicLineOutliner',
      displayName: 'Line Master',
      description: 'Master fundamental line drawing techniques for glass painting',
      specialty: 'Basic Lines',
      imagePath: 'assets/gl/gl_1.png',
      glassColor: const Color(0xFF20B2AA),
      accentColor: const Color(0xFF87CEEB),
    ),
    AiCoach34Model(
      id: 2,
      name: 'ColorGradientMixer',
      displayName: 'Color Wizard',
      description: 'Create smooth color gradients and blending techniques',
      specialty: 'Color Blending',
      imagePath: 'assets/gl/gl_2.png',
      glassColor: const Color(0xFF4682B4),
      accentColor: const Color(0xFFB0E0E6),
    ),
    AiCoach34Model(
      id: 3,
      name: 'OutlineAccentCoach',
      displayName: 'Edge Artist',
      description: 'Perfect outlining techniques to define painting details',
      specialty: 'Outline Design',
      imagePath: 'assets/gl/gl_3.png',
      glassColor: const Color(0xFF2E8B57),
      accentColor: const Color(0xFF98FB98),
    ),
    AiCoach34Model(
      id: 4,
      name: 'GlassTextureIllustrator',
      displayName: 'Texture Pro',
      description: 'Depict realistic glass texture and sheen effects',
      specialty: 'Glass Texture',
      imagePath: 'assets/gl/gl_4.png',
      glassColor: const Color(0xFF6495ED),
      accentColor: const Color(0xFFE6E6FA),
    ),
    AiCoach34Model(
      id: 5,
      name: 'MiniPatternCreator',
      displayName: 'Pattern Genius',
      description: 'Design intricate small patterns for glassware',
      specialty: 'Mini Patterns',
      imagePath: 'assets/gl/gl_5.png',
      glassColor: const Color(0xFF40E0D0),
      accentColor: const Color(0xFFAFEEEE),
    ),
    AiCoach34Model(
      id: 6,
      name: 'ThemeSceneConstructor',
      displayName: 'Scene Builder',
      description: 'Build cohesive themed scenes in glass painting',
      specialty: 'Scene Design',
      imagePath: 'assets/gl/gl_6.png',
      glassColor: const Color(0xFF5F9EA0),
      accentColor: const Color(0xFFB0C4DE),
    ),
    AiCoach34Model(
      id: 7,
      name: 'PaintPropertyUtilizer',
      displayName: 'Paint Expert',
      description: 'Master glass paint properties for unique effects',
      specialty: 'Paint Effects',
      imagePath: 'assets/gl/gl_7.png',
      glassColor: const Color(0xFF708090),
      accentColor: const Color(0xFFD3D3D3),
    ),
    AiCoach34Model(
      id: 8,
      name: 'DecorativeBorderInstructor',
      displayName: 'Border Master',
      description: 'Create beautiful decorative borders for glass items',
      specialty: 'Border Design',
      imagePath: 'assets/gl/gl_8.png',
      glassColor: const Color(0xFF4169E1),
      accentColor: const Color(0xFFDDA0DD),
    ),
    AiCoach34Model(
      id: 9,
      name: 'MistakeCorrectionGuide',
      displayName: 'Fix Specialist',
      description: 'Fix common glass painting mistakes with ease',
      specialty: 'Error Correction',
      imagePath: 'assets/gl/gl_9.png',
      glassColor: const Color(0xFF32CD32),
      accentColor: const Color(0xFF90EE90),
    ),
    AiCoach34Model(
      id: 10,
      name: 'CuringAndProtectionAdvisor',
      displayName: 'Care Expert',
      description: 'Proper curing and protection for lasting artworks',
      specialty: 'Care & Protection',
      imagePath: 'assets/gl/gl_10.png',
      glassColor: const Color(0xFF8A2BE2),
      accentColor: const Color(0xFFDDA0DD),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _initializeFavoriteManager();
    _startAnimations();
  }

  /// 初始化收藏管理器
  void _initializeFavoriteManager() async {
    await _favoriteManager.initialize();
  }

  void _initAnimations() {
    _fadeCtrl = AnimationController(
      duration: const Duration(milliseconds: 1800),
      vsync: this,
    );
    
    _shimmerCtrl = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeCtrl,
      curve: Curves.easeOutQuart,
    ));

    _shimmerAnimation = Tween<double>(
      begin: -2.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _shimmerCtrl,
      curve: Curves.easeInOutSine,
    ));
  }

  void _startAnimations() {
    _fadeCtrl.forward();
    _shimmerCtrl.repeat(reverse: true);
  }

  @override
  void dispose() {
    _fadeCtrl.dispose();
    _shimmerCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: GleArtisticBg67(
        primaryColor: const Color(0xFF87CEEB),
        secondaryColor: const Color(0xFFB0E0E6),
        accentColor: const Color(0xFFFFF8DC),
        includeFloatingElements: false, // 主页不需要浮动元素，卡片本身就很丰富
        backgroundImage: 'assets/bg2.png', // 添加背景图片
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                // 增强版标题区域
                _buildEnhancedHeader(),
                
                // 创新卡片布局
                _buildInnovativeCoachGrid(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建增强版标题区域
  Widget _buildEnhancedHeader() {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.fromLTRB(20, 20, 20, 30),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(30),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 28),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.25),
                    Colors.white.withOpacity(0.05),
                    Colors.black.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 2,
                ),
              ),
              child: Column(
                children: [
                  ShaderMask(
                    shaderCallback: (bounds) => LinearGradient(
                      colors: [
                        const Color(0xFF87CEEB),
                        const Color(0xFFFFD700),
                        const Color(0xFF87CEEB),
                      ],
                    ).createShader(bounds),
                    child: const Text(
                      'Glass Art Masters',
                      style: TextStyle(
                        fontSize: 36,
                        fontWeight: FontWeight.w900,
                        color: Colors.white,
                        letterSpacing: 1.5,
                        height: 1.1,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Select your creative mentor',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.white.withOpacity(0.85),
                      letterSpacing: 1.0,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建创新卡片网格布局
  Widget _buildInnovativeCoachGrid() {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final coach = _aiCoaches[index];
            return _buildInnovativeCoachCard(coach, index);
          },
          childCount: _aiCoaches.length,
        ),
      ),
    );
  }

  /// 构建创新AI教练卡片
  Widget _buildInnovativeCoachCard(AiCoach34Model coach, int index) {
    // 不同的卡片布局样式
    final isLargeCard = index % 3 == 0; // 每3个中有1个大卡片
    final isRightAlign = index % 2 == 1; // 奇数索引右对齐
    final cardHeight = isLargeCard ? 320.0 : 280.0;
    
    return Container(
      margin: EdgeInsets.only(
        bottom: 24,
        left: isRightAlign ? 40 : 8,
        right: isRightAlign ? 8 : 40,
      ),
      child: TweenAnimationBuilder<double>(
        duration: Duration(milliseconds: 1200 + index * 150),
        tween: Tween<double>(begin: 0.0, end: 1.0),
        builder: (context, value, child) {
          return Transform.translate(
            offset: Offset(
              isRightAlign ? 80 * (1 - value) : -80 * (1 - value),
              30 * (1 - value),
            ),
            child: Transform.scale(
              scale: 0.8 + 0.2 * value,
              child: Opacity(
                opacity: value,
                child: child,
              ),
            ),
          );
        },
        child: GestureDetector(
          onTap: () => _onCoachTap(coach),
          child: _buildEnhancedGlassCard(coach, index, cardHeight, isLargeCard),
        ),
      ),
    );
  }

  /// 构建增强玻璃卡片
  Widget _buildEnhancedGlassCard(AiCoach34Model coach, int index, double height, bool isLarge) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(25),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 25, sigmaY: 25),
        child: Container(
          height: height,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                coach.glassColor.withOpacity(0.3),
                coach.accentColor.withOpacity(0.15),
                Colors.white.withOpacity(0.1),
                Colors.black.withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: coach.glassColor.withOpacity(0.4),
              width: 1.5,
            ),
          ),
          child: Stack(
            children: [
              // 增强霓虹效果
              _buildEnhancedNeonEffect(coach),
              
              // 主要内容 - 使用Flex布局避免溢出
              Padding(
                padding: const EdgeInsets.all(18),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 专业标签
                    _buildEnhancedSpecialtyTag(coach),
                    
                    // 大头像区域 - 使用Expanded避免溢出
                    Expanded(
                      child: Center(
                        child: _buildLargeAvatar(coach, isLarge),
                      ),
                    ),
                    
                    // 角色信息 - 固定高度
                    SizedBox(
                      height: 80, // 固定高度避免溢出
                      child: _buildEnhancedCoachInfo(coach),
                    ),
                  ],
                ),
              ),
              
              // 3D玻璃反光效果
              _buildGlassReflection(),
              
              // 悬浮装饰升级
              _buildEnhancedDecorations(coach, index),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建增强霓虹效果
  Widget _buildEnhancedNeonEffect(AiCoach34Model coach) {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              gradient: LinearGradient(
                begin: Alignment(-2.0 + _shimmerAnimation.value, -1.0),
                end: Alignment(2.0 + _shimmerAnimation.value, 1.0),
                colors: [
                  Colors.transparent,
                  coach.glassColor.withOpacity(0.15),
                  coach.accentColor.withOpacity(0.1),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.4, 0.6, 1.0],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建增强专业标签
  Widget _buildEnhancedSpecialtyTag(AiCoach34Model coach) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(18),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 6),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                coach.glassColor.withOpacity(0.4),
                coach.accentColor.withOpacity(0.2),
              ],
            ),
            borderRadius: BorderRadius.circular(18),
            border: Border.all(
              color: coach.glassColor.withOpacity(0.6),
              width: 1,
            ),
          ),
          child: Text(
            coach.specialty,
            style: TextStyle(
              fontSize: 12,
              color: Colors.white.withOpacity(0.95),
              fontWeight: FontWeight.w700,
              letterSpacing: 0.5,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建大头像
  Widget _buildLargeAvatar(AiCoach34Model coach, bool isLarge) {
    final size = isLarge ? 130.0 : 110.0; // 稍微减小尺寸
    
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            coach.glassColor.withOpacity(0.4),
            coach.accentColor.withOpacity(0.2),
            Colors.transparent,
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
        boxShadow: [
          BoxShadow(
            color: coach.glassColor.withOpacity(0.4),
            blurRadius: 20,
            spreadRadius: 6,
          ),
          BoxShadow(
            color: coach.accentColor.withOpacity(0.3),
            blurRadius: 30,
            spreadRadius: 10,
          ),
        ],
      ),
      child: Container(
        margin: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 2,
          ),
        ),
        child: ClipOval(
          child: Image.asset(
            coach.imagePath,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      coach.glassColor,
                      coach.accentColor,
                    ],
                  ),
                ),
                child: Icon(
                  CupertinoIcons.person_circle_fill,
                  size: size * 0.6,
                  color: Colors.white.withOpacity(0.8),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 构建增强角色信息 - 调整布局避免被按钮遮挡
  Widget _buildEnhancedCoachInfo(AiCoach34Model coach) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min, // 防止溢出
      children: [
        // 角色名称
        Text(
          coach.displayName,
          style: const TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.w800,
            color: Colors.white,
            letterSpacing: 0.5,
            height: 1.1,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        
        const SizedBox(height: 6),
        
        // 描述 - 添加右边距避免被按钮遮挡
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(right: 60), // 为右下角按钮留出空间
            child: Text(
              coach.description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.white.withOpacity(0.8),
                height: 1.3,
                fontWeight: FontWeight.w400,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建玻璃反光效果
  Widget _buildGlassReflection() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.center,
            colors: [
              Colors.white.withOpacity(0.1),
              Colors.transparent,
            ],
          ),
        ),
      ),
    );
  }

  /// 构建增强装饰 - 简化版本
  Widget _buildEnhancedDecorations(AiCoach34Model coach, int index) {
    return Stack(
      children: [
        // 右上角脉冲装饰 - 使用简单的静态装饰
        Positioned(
          top: 20,
          right: 20,
          child: Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: coach.accentColor.withOpacity(0.8),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: coach.accentColor.withOpacity(0.6),
                  blurRadius: 12,
                  spreadRadius: 4,
                ),
              ],
            ),
          ),
        ),
        
        // 右下角按钮组
        Positioned(
          bottom: 15,
          right: 15,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 收藏按钮
              StreamBuilder<List<String>>(
                stream: _favoriteManager.favoritesStream,
                builder: (context, snapshot) {
                  final isFavorite = _favoriteManager.isFavorite(coach.name);
                  return GestureDetector(
                    onTap: () => _onFavoriteTap(coach),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(18),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                        child: Container(
                          width: 36,
                          height: 36,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: isFavorite
                                ? [
                                    Colors.red.withOpacity(0.4),
                                    Colors.pink.withOpacity(0.2),
                                  ]
                                : [
                                    Colors.white.withOpacity(0.3),
                                    Colors.white.withOpacity(0.1),
                                  ],
                            ),
                            borderRadius: BorderRadius.circular(18),
                            border: Border.all(
                              color: isFavorite
                                ? Colors.red.withOpacity(0.4)
                                : Colors.white.withOpacity(0.4),
                              width: 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: isFavorite
                                  ? Colors.red.withOpacity(0.2)
                                  : Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            isFavorite ? CupertinoIcons.heart_fill : CupertinoIcons.heart,
                            color: isFavorite
                              ? Colors.red.withOpacity(0.9)
                              : Colors.white.withOpacity(0.9),
                            size: 18,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(width: 8),

              // 详情按钮
              GestureDetector(
                onTap: () => _onDetailsTap(coach),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(18),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                    child: Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white.withOpacity(0.3),
                            Colors.white.withOpacity(0.1),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(18),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.4),
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        CupertinoIcons.info_circle,
                        color: Colors.white.withOpacity(0.9),
                        size: 18,
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // 直接聊天按钮
              GestureDetector(
                onTap: () => _onChatTap(coach),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                    child: Container(
                      width: 44,
                      height: 36,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            coach.glassColor.withOpacity(0.4),
                            coach.accentColor.withOpacity(0.2),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.4),
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: coach.glassColor.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        CupertinoIcons.chat_bubble_fill,
                        color: Colors.white.withOpacity(0.9),
                        size: 18,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 详情按钮点击事件
  void _onDetailsTap(AiCoach34Model coach) {
    print('🎨 View details for coach: ${coach.displayName}');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GleCoachDetai88Vi(coach: coach),
      ),
    );
  }

  /// 直接聊天按钮点击事件
  void _onChatTap(AiCoach34Model coach) {
    print('🎨 Start chat with coach: ${coach.displayName}');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GleChatVi45(coach: coach),
      ),
    );
  }

  /// 收藏按钮点击事件
  void _onFavoriteTap(AiCoach34Model coach) async {
    await _favoriteManager.toggleFavorite(coach.name);

    // 显示收藏状态提示
    if (mounted) {
      final isFavorite = _favoriteManager.isFavorite(coach.name);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isFavorite
              ? '❤️ Added ${coach.displayName} to favorites'
              : '💔 Removed ${coach.displayName} from favorites',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: isFavorite
            ? Colors.red.withOpacity(0.8)
            : Colors.grey.withOpacity(0.8),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }
}

/// AI教练数据模型
class AiCoach34Model {
  final int id;
  final String name;
  final String displayName;
  final String description;
  final String specialty;
  final String imagePath;
  final Color glassColor;
  final Color accentColor;

  AiCoach34Model({
    required this.id,
    required this.name,
    required this.displayName,
    required this.description,
    required this.specialty,
    required this.imagePath,
    required this.glassColor,
    required this.accentColor,
  });
} 