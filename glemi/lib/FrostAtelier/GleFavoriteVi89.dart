import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'dart:math' as math;
import '../CulletParts/GleArtisticBg67.dart';
import '../PrismVault/GleFavoriteDataMgr67.dart';
import '../FrostAtelier/GleMainVi78.dart';
import 'GleCoachDetai88Vi.dart';
import 'GleChatVi45.dart';

/// Glemi收藏页面 - 玻璃画艺术收藏展示
/// 采用创新的六边形蜂窝布局，模拟玻璃碎片拼接效果
class GleFavoriteVi89 extends StatefulWidget {
  const GleFavoriteVi89({super.key});

  @override
  State<GleFavoriteVi89> createState() => _GleFavoriteVi89State();
}

class _GleFavoriteVi89State extends State<GleFavoriteVi89>
    with TickerProviderStateMixin {
  
  final GleFavoriteDataMgr67 _favoriteManager = GleFavoriteDataMgr67();
  List<String> _favoriteCoaches = [];
  
  late AnimationController _fadeCtrl;
  late AnimationController _floatCtrl;
  late AnimationController _shimmerCtrl;
  
  late Animation<double> _fadeAnimation;
  late Animation<double> _floatAnimation;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _initializeFavoriteManager();
    _loadFavorites();
    _startAnimations();

    // 监听收藏变化
    _favoriteManager.favoritesStream.listen((favorites) {
      if (mounted) {
        setState(() {
          _favoriteCoaches = favorites;
        });
      }
    });
  }

  /// 初始化收藏管理器
  void _initializeFavoriteManager() async {
    await _favoriteManager.initialize();
  }

  void _initAnimations() {
    _fadeCtrl = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _floatCtrl = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );
    
    _shimmerCtrl = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeCtrl,
      curve: Curves.easeOut,
    ));
    
    _floatAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _floatCtrl,
      curve: Curves.linear,
    ));
    
    _shimmerAnimation = Tween<double>(
      begin: -2.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _shimmerCtrl,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimations() {
    _fadeCtrl.forward();
    _floatCtrl.repeat();
    _shimmerCtrl.repeat(reverse: true);
  }

  void _loadFavorites() {
    setState(() {
      _favoriteCoaches = _favoriteManager.currentFavorites;
    });
  }

  @override
  void dispose() {
    _fadeCtrl.dispose();
    _floatCtrl.dispose();
    _shimmerCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildGlassAppBar(),
      body: GleArtisticBg67(
        primaryColor: const Color(0xFF87CEEB),
        secondaryColor: const Color(0xFFB0E0E6),
        accentColor: const Color(0xFFFFF8DC),
        includeFloatingElements: true,
        backgroundImage: 'assets/bg2.png', // 添加背景图片
        backgroundFit: BoxFit.fill, // 确保在iPad上正确显示
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: _favoriteCoaches.isEmpty
                ? _buildEmptyState()
                : _buildFavoriteGrid(),
          ),
        ),
      ),
    );
  }

  /// 构建玻璃拟态AppBar
  PreferredSizeWidget _buildGlassAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(60),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: AppBar(
            backgroundColor: Colors.white.withOpacity(0.1),
            elevation: 0,
            // leading: IconButton(
            //   icon: Icon(
            //     CupertinoIcons.back,
            //     color: Colors.white.withOpacity(0.9),
            //     size: 24,
            //   ),
            //   onPressed: () => Navigator.of(context).pop(),
            // ),
            title: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  CupertinoIcons.heart_fill,
                  color: Colors.red.withOpacity(0.8),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'My Favorite Masters',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            centerTitle: true,
            actions: [
              Container(
                margin: const EdgeInsets.only(right: 16),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.red.withOpacity(0.3),
                      Colors.pink.withOpacity(0.2),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${_favoriteCoaches.length}',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _floatAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, math.sin(_floatAnimation.value) * 10),
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        Colors.red.withOpacity(0.3),
                        Colors.pink.withOpacity(0.1),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: Icon(
                    CupertinoIcons.heart,
                    size: 60,
                    color: Colors.white.withOpacity(0.6),
                  ),
                ),
              );
            },
          ),
          
          const SizedBox(height: 24),
          
          // Text(
          //   'No Favorite Masters Yet',
          //   style: TextStyle(
          //     color: Colors.white.withOpacity(0.9),
          //     fontSize: 24,
          //     fontWeight: FontWeight.w600,
          //   ),
          // ),
          //
          // const SizedBox(height: 12),
          
          Text(
            'Discover amazing glass painting coaches\nand add them to your favorites!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 16,
              height: 1.4,
            ),
          ),
          
          // const SizedBox(height: 32),
          //
          // ClipRRect(
          //   borderRadius: BorderRadius.circular(25),
          //   child: BackdropFilter(
          //     filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          //     child: Container(
          //       padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          //       decoration: BoxDecoration(
          //         gradient: LinearGradient(
          //           colors: [
          //             Colors.white.withOpacity(0.2),
          //             Colors.white.withOpacity(0.1),
          //           ],
          //         ),
          //         borderRadius: BorderRadius.circular(25),
          //         border: Border.all(
          //           color: Colors.white.withOpacity(0.3),
          //           width: 1,
          //         ),
          //       ),
          //       child: Text(
          //         'Explore Coaches',
          //         style: TextStyle(
          //           color: Colors.white.withOpacity(0.9),
          //           fontSize: 16,
          //           fontWeight: FontWeight.w600,
          //         ),
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  /// 构建收藏网格布局 - 玻璃碎片拼接效果
  Widget _buildFavoriteGrid() {
    // 获取所有AI教练数据
    final allCoaches = _getAllCoaches();
    final favoriteCoaches = allCoaches.where((coach) =>
      _favoriteCoaches.contains(coach.name)).toList();

    return CustomScrollView(
      physics: const BouncingScrollPhysics(),
      slivers: [
        // 标题区域
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    AnimatedBuilder(
                      animation: _shimmerAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: 1.0 + (_shimmerAnimation.value * 0.1),
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: RadialGradient(
                                colors: [
                                  Colors.red.withOpacity(0.3),
                                  Colors.pink.withOpacity(0.1),
                                ],
                              ),
                            ),
                            child: Icon(
                              CupertinoIcons.heart_fill,
                              color: Colors.red.withOpacity(0.8),
                              size: 24,
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Favorite Masters',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 28,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '${favoriteCoaches.length} Glass Art Specialists',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ),

        // 六边形蜂窝网格
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.85,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final coach = favoriteCoaches[index];
                return _buildHexagonalCoachCard(coach, index);
              },
              childCount: favoriteCoaches.length,
            ),
          ),
        ),

        // 底部间距
        const SliverToBoxAdapter(
          child: SizedBox(height: 100),
        ),
      ],
    );
  }

  /// 构建六边形教练卡片
  Widget _buildHexagonalCoachCard(AiCoach34Model coach, int index) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            math.sin(_floatAnimation.value + index * 0.5) * 5,
            math.cos(_floatAnimation.value + index * 0.3) * 3,
          ),
          child: GestureDetector(
            onTap: () => _navigateToCoachDetail(coach),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        coach.glassColor.withOpacity(0.3),
                        coach.accentColor.withOpacity(0.2),
                        Colors.white.withOpacity(0.1),
                      ],
                    ),
                    border: Border.all(
                      color: coach.glassColor.withOpacity(0.4),
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Stack(
                    children: [
                      // 背景装饰
                      _buildCardDecorations(coach, index),

                      // 主要内容
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // 头像
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: RadialGradient(
                                  colors: [
                                    coach.glassColor.withOpacity(0.8),
                                    coach.accentColor.withOpacity(0.6),
                                  ],
                                ),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.3),
                                  width: 2,
                                ),
                              ),
                              child: ClipOval(
                                child: Image.asset(
                                  coach.imagePath,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      color: coach.glassColor.withOpacity(0.3),
                                      child: Icon(
                                        CupertinoIcons.paintbrush_fill,
                                        color: Colors.white.withOpacity(0.8),
                                        size: 32,
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),

                            const SizedBox(height: 12),

                            // 名称
                            Text(
                              coach.displayName,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),

                            const SizedBox(height: 4),

                            // 专业领域
                            Text(
                              coach.specialty,
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.7),
                                fontSize: 12,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),

                      // 收藏按钮
                      Positioned(
                        top: 8,
                        right: 8,
                        child: GestureDetector(
                          onTap: () => _toggleFavoriteInGrid(coach),
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.black.withOpacity(0.3),
                            ),
                            child: Icon(
                              CupertinoIcons.heart_fill,
                              color: Colors.red.withOpacity(0.9),
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建卡片装饰
  Widget _buildCardDecorations(AiCoach34Model coach, int index) {
    return Stack(
      children: [
        // 左上角装饰
        Positioned(
          top: -20,
          left: -20,
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  coach.glassColor.withOpacity(0.3),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),

        // 右下角装饰
        Positioned(
          bottom: -15,
          right: -15,
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  coach.accentColor.withOpacity(0.4),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 获取所有AI教练数据
  List<AiCoach34Model> _getAllCoaches() {
    return [
      AiCoach34Model(
        id: 1,
        name: 'BasicLineOutliner',
        displayName: 'Line Master',
        description: 'Master fundamental line drawing techniques for glass painting',
        specialty: 'Basic Lines',
        imagePath: 'assets/gl/gl_1.png',
        glassColor: const Color(0xFF20B2AA),
        accentColor: const Color(0xFF87CEEB),
      ),
      AiCoach34Model(
        id: 2,
        name: 'ColorGradientMixer',
        displayName: 'Color Wizard',
        description: 'Create smooth color gradients and blending techniques',
        specialty: 'Color Blending',
        imagePath: 'assets/gl/gl_2.png',
        glassColor: const Color(0xFF4682B4),
        accentColor: const Color(0xFFB0E0E6),
      ),
      AiCoach34Model(
        id: 3,
        name: 'OutlineAccentCoach',
        displayName: 'Edge Artist',
        description: 'Perfect outlining techniques to define painting details',
        specialty: 'Outline Design',
        imagePath: 'assets/gl/gl_3.png',
        glassColor: const Color(0xFF2E8B57),
        accentColor: const Color(0xFF98FB98),
      ),
      AiCoach34Model(
        id: 4,
        name: 'GlassTextureIllustrator',
        displayName: 'Texture Pro',
        description: 'Depict realistic glass texture and sheen effects',
        specialty: 'Glass Texture',
        imagePath: 'assets/gl/gl_4.png',
        glassColor: const Color(0xFF6495ED),
        accentColor: const Color(0xFFE6E6FA),
      ),
      AiCoach34Model(
        id: 5,
        name: 'MiniPatternCreator',
        displayName: 'Pattern Genius',
        description: 'Design intricate small patterns for glassware',
        specialty: 'Mini Patterns',
        imagePath: 'assets/gl/gl_5.png',
        glassColor: const Color(0xFF40E0D0),
        accentColor: const Color(0xFFAFEEEE),
      ),
      AiCoach34Model(
        id: 6,
        name: 'ThemeSceneConstructor',
        displayName: 'Scene Builder',
        description: 'Build cohesive themed scenes in glass painting',
        specialty: 'Scene Design',
        imagePath: 'assets/gl/gl_6.png',
        glassColor: const Color(0xFF5F9EA0),
        accentColor: const Color(0xFFB0C4DE),
      ),
      AiCoach34Model(
        id: 7,
        name: 'PaintPropertyUtilizer',
        displayName: 'Paint Expert',
        description: 'Master glass paint properties for unique effects',
        specialty: 'Paint Effects',
        imagePath: 'assets/gl/gl_7.png',
        glassColor: const Color(0xFF708090),
        accentColor: const Color(0xFFD3D3D3),
      ),
      AiCoach34Model(
        id: 8,
        name: 'DecorativeBorderInstructor',
        displayName: 'Border Master',
        description: 'Create beautiful decorative borders for glass items',
        specialty: 'Border Design',
        imagePath: 'assets/gl/gl_8.png',
        glassColor: const Color(0xFF4169E1),
        accentColor: const Color(0xFFDDA0DD),
      ),
      AiCoach34Model(
        id: 9,
        name: 'MistakeCorrectionGuide',
        displayName: 'Fix Specialist',
        description: 'Fix common glass painting mistakes with ease',
        specialty: 'Error Correction',
        imagePath: 'assets/gl/gl_9.png',
        glassColor: const Color(0xFF32CD32),
        accentColor: const Color(0xFF90EE90),
      ),
      AiCoach34Model(
        id: 10,
        name: 'CuringAndProtectionAdvisor',
        displayName: 'Care Expert',
        description: 'Proper curing and protection for lasting artworks',
        specialty: 'Care & Protection',
        imagePath: 'assets/gl/gl_10.png',
        glassColor: const Color(0xFF9370DB),
        accentColor: const Color(0xFFDDA0DD),
      ),
    ];
  }

  /// 导航到教练详情页
  void _navigateToCoachDetail(AiCoach34Model coach) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GleCoachDetai88Vi(coach: coach),
      ),
    );
  }

  /// 在网格中切换收藏状态
  void _toggleFavoriteInGrid(AiCoach34Model coach) async {
    // 先获取当前状态
    final wasAlreadyFavorite = _favoriteManager.isFavorite(coach.name);

    // 切换收藏状态
    await _favoriteManager.toggleFavorite(coach.name);

    // 显示收藏状态提示（基于之前的状态）
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            !wasAlreadyFavorite
              ? '❤️ Added ${coach.displayName} to favorites'
              : '💔 Removed ${coach.displayName} from favorites',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: !wasAlreadyFavorite
            ? Colors.red.withOpacity(0.8)
            : Colors.grey.withOpacity(0.8),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }
}
