import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'dart:math' as math;
import '../CulletParts/GleArtisticBg67.dart';
import '../PrismVault/GleFavoriteDataMgr67.dart';
import '../FrostAtelier/GleMainVi78.dart';
import 'GleCoachDetai88Vi.dart';
import 'GleChatVi45.dart';

/// Glemi收藏页面 - 玻璃画艺术收藏展示
/// 采用创新的六边形蜂窝布局，模拟玻璃碎片拼接效果
class GleFavoriteVi89 extends StatefulWidget {
  const GleFavoriteVi89({super.key});

  @override
  State<GleFavoriteVi89> createState() => _GleFavoriteVi89State();
}

class _GleFavoriteVi89State extends State<GleFavoriteVi89>
    with TickerProviderStateMixin {
  
  final GleFavoriteDataMgr67 _favoriteManager = GleFavoriteDataMgr67();
  List<String> _favoriteCoaches = [];
  
  late AnimationController _fadeCtrl;
  late AnimationController _floatCtrl;
  late AnimationController _shimmerCtrl;
  
  late Animation<double> _fadeAnimation;
  late Animation<double> _floatAnimation;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _loadFavorites();
    _startAnimations();
    
    // 监听收藏变化
    _favoriteManager.favoritesStream.listen((favorites) {
      if (mounted) {
        setState(() {
          _favoriteCoaches = favorites;
        });
      }
    });
  }

  void _initAnimations() {
    _fadeCtrl = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _floatCtrl = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );
    
    _shimmerCtrl = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeCtrl,
      curve: Curves.easeOut,
    ));
    
    _floatAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _floatCtrl,
      curve: Curves.linear,
    ));
    
    _shimmerAnimation = Tween<double>(
      begin: -2.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _shimmerCtrl,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimations() {
    _fadeCtrl.forward();
    _floatCtrl.repeat();
    _shimmerCtrl.repeat(reverse: true);
  }

  void _loadFavorites() {
    setState(() {
      _favoriteCoaches = _favoriteManager.currentFavorites;
    });
  }

  @override
  void dispose() {
    _fadeCtrl.dispose();
    _floatCtrl.dispose();
    _shimmerCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildGlassAppBar(),
      body: GleArtisticBg67(
        primaryColor: const Color(0xFF87CEEB),
        secondaryColor: const Color(0xFFB0E0E6),
        accentColor: const Color(0xFFFFF8DC),
        includeFloatingElements: true,
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: _favoriteCoaches.isEmpty
                ? _buildEmptyState()
                : _buildFavoriteGrid(),
          ),
        ),
      ),
    );
  }

  /// 构建玻璃拟态AppBar
  PreferredSizeWidget _buildGlassAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(60),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: AppBar(
            backgroundColor: Colors.white.withOpacity(0.1),
            elevation: 0,
            leading: IconButton(
              icon: Icon(
                CupertinoIcons.back,
                color: Colors.white.withOpacity(0.9),
                size: 24,
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
            title: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  CupertinoIcons.heart_fill,
                  color: Colors.red.withOpacity(0.8),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'My Favorite Masters',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            centerTitle: true,
            actions: [
              Container(
                margin: const EdgeInsets.only(right: 16),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.red.withOpacity(0.3),
                      Colors.pink.withOpacity(0.2),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${_favoriteCoaches.length}',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _floatAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, math.sin(_floatAnimation.value) * 10),
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        Colors.red.withOpacity(0.3),
                        Colors.pink.withOpacity(0.1),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: Icon(
                    CupertinoIcons.heart,
                    size: 60,
                    color: Colors.white.withOpacity(0.6),
                  ),
                ),
              );
            },
          ),
          
          const SizedBox(height: 24),
          
          Text(
            'No Favorite Masters Yet',
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 24,
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 12),
          
          Text(
            'Discover amazing glass painting coaches\nand add them to your favorites!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 16,
              height: 1.4,
            ),
          ),
          
          const SizedBox(height: 32),
          
          ClipRRect(
            borderRadius: BorderRadius.circular(25),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.white.withOpacity(0.2),
                      Colors.white.withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  'Explore Coaches',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
