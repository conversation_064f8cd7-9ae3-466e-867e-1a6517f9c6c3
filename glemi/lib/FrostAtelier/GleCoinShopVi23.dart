import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'dart:async';
import 'dart:math' as randomizationCore;
import 'package:in_app_purchase/in_app_purchase.dart';
import '../CulletParts/GleArtisticBg67.dart';
import '../PrismVault/GleCoinDataMgr45.dart';

/// Glemi金币商城页面
/// 支持iOS内购功能的高级UI设计
class GleCoinShopVi23 extends StatefulWidget {
  const GleCoinShopVi23({super.key});

  @override
  State<GleCoinShopVi23> createState() => _GleCoinShopVi23State();
}

class _GleCoinShopVi23State extends State<GleCoinShopVi23>
    with TickerProviderStateMixin {
  
  late AnimationController _fadeCtrl;
  late AnimationController _pulseCtrl;
  late AnimationController _rotateCtrl;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotateAnimation;
  
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _purchaseSubscription;
  
  List<SCGoods> _products = [];
  bool _isLoading = false;
  String? _currentPurchasingProduct;
  int _currentCoinBalance = 0;

  // 防抖动和订单验证
  final Set<String> _processedTransactionIds = <String>{};
  DateTime? _lastPurchaseAttempt;
  static const Duration _purchaseCooldown = Duration(seconds: 3);

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _initializeData();
    _startAnimations();
    _listenToPurchases();
    _generateUniqueAntiDuplicationCode();
  }

  void _initAnimations() {
    _fadeCtrl = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _pulseCtrl = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _rotateCtrl = AnimationController(
      duration: const Duration(milliseconds: 8000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeCtrl,
      curve: Curves.easeOutQuart,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseCtrl,
      curve: Curves.easeInOutSine,
    ));
    
    _rotateAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotateCtrl,
      curve: Curves.linear,
    ));
  }

  void _startAnimations() {
    _fadeCtrl.forward();
    _pulseCtrl.repeat(reverse: true);
    _rotateCtrl.repeat();
  }

  void _initializeData() {
    _products = GleCoinDataMgr45.getAllProducts();
    _currentCoinBalance = GleCoinDataMgr45().currentBalance;
    
    // 监听金币余额变化
    GleCoinDataMgr45().coinBalanceStream.listen((balance) {
      if (mounted) {
        setState(() {
          _currentCoinBalance = balance;
        });
      }
    });
  }

  void _listenToPurchases() {
    _purchaseSubscription = _inAppPurchase.purchaseStream.listen(
      _handlePurchaseUpdates,
      onError: (error) {
        print('Purchase stream error: $error');
        _handlePurchaseError('Purchase stream error occurred');
      },
    );
  }

  Future<void> _handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) async {
    for (final purchase in purchaseDetailsList) {
      await _processPurchase(purchase);
    }
  }

  Future<void> _processPurchase(PurchaseDetails purchase) async {
    try {
      switch (purchase.status) {
        case PurchaseStatus.pending:
          print('Purchase pending: ${purchase.productID}');
          // 保持加载状态
          break;
          
        case PurchaseStatus.purchased:
          print('Purchase successful: ${purchase.productID}');
          await _handleSuccessfulPurchase(purchase);
          break;
          
        case PurchaseStatus.error:
          print('Purchase error: ${purchase.error}');
          await _handlePurchaseError('Purchase failed. Please try again.');
          break;
          
        case PurchaseStatus.canceled:
          print('Purchase canceled by user: ${purchase.productID}');
          await _handlePurchaseCanceled();
          break;
          
        case PurchaseStatus.restored:
          print('Purchase restored: ${purchase.productID}');
          // 处理恢复购买逻辑（如果需要）
          break;
      }
    } catch (e) {
      // 检查是否为用户取消交易
      final errorMessage = e.toString().toLowerCase();
      if (errorMessage.contains('cancelled') || 
          errorMessage.contains('canceled') ||
          errorMessage.contains('storekit2_purchase_cancelled')) {
        await _handlePurchaseCanceled();
      } else {
        print('购买失败: $e');
        await _handlePurchaseError('Purchase processing failed');
      }
    } finally {
      // 确保完成交易
      if (purchase.pendingCompletePurchase) {
        await _inAppPurchase.completePurchase(purchase);
      }
      
      // 清理加载状态
      if (mounted) {
        setState(() {
          _isLoading = false;
          _currentPurchasingProduct = null;
        });
      }
    }
  }

  Future<void> _handleSuccessfulPurchase(PurchaseDetails purchase) async {
    // 订单验证 - 检查交易ID是否已处理
    final transactionId = purchase.purchaseID ?? purchase.verificationData.localVerificationData;
    if (transactionId != null && _processedTransactionIds.contains(transactionId)) {
      print('Duplicate transaction detected: $transactionId, ignoring');
      return;
    }

    // 验证商品码和订单信息
    final product = GleCoinDataMgr45.getProductByCode(purchase.productID);
    if (product == null) {
      print('Invalid product code: ${purchase.productID}');
      await _handlePurchaseError('Invalid product information');
      return;
    }

    // 验证当前购买的商品是否匹配
    if (_currentPurchasingProduct != null && _currentPurchasingProduct != purchase.productID) {
      print('Product mismatch: expected $_currentPurchasingProduct, got ${purchase.productID}');
      await _handlePurchaseError('Product verification failed');
      return;
    }

    try {
      final coinAmount = int.parse(product.exchangeCoin);

      // 记录交易ID防止重复处理
      if (transactionId != null) {
        _processedTransactionIds.add(transactionId);
      }

      // 添加金币并记录交易信息
      await GleCoinDataMgr45().addCoins(
        coinAmount,
        productId: purchase.productID,
        transactionId: transactionId,
      );

      print('Purchase completed successfully: ${purchase.productID}, coins: $coinAmount, transaction: $transactionId');

      if (mounted) {
        setState(() {
          _isLoading = false;
          _currentPurchasingProduct = null;
        });

        _showSuccessDialog(coinAmount);
      }
    } catch (e) {
      print('Error processing successful purchase: $e');
      await _handlePurchaseError('Failed to process purchase');
    }
  }

  Future<void> _handlePurchaseError(String message) async {
    if (mounted) {
      setState(() {
        _isLoading = false;
        _currentPurchasingProduct = null;
      });
      
      _showErrorDialog(message);
    }
  }

  Future<void> _handlePurchaseCanceled() async {
    if (mounted) {
      setState(() {
        _isLoading = false;
        _currentPurchasingProduct = null;
      });
      
      _showCancelDialog();
    }
  }

  Future<void> _purchaseProduct(SCGoods product) async {
    // 防抖动检查
    if (_isLoading) {
      print('Purchase already in progress, ignoring duplicate request');
      return;
    }

    // 冷却时间检查
    final now = DateTime.now();
    if (_lastPurchaseAttempt != null &&
        now.difference(_lastPurchaseAttempt!) < _purchaseCooldown) {
      print('Purchase cooldown active, please wait');
      _showErrorDialog('Please wait a moment before making another purchase');
      return;
    }

    _lastPurchaseAttempt = now;

    setState(() {
      _isLoading = true;
      _currentPurchasingProduct = product.code;
    });

    try {
      final bool available = await _inAppPurchase.isAvailable();
      if (!available) {
        throw Exception('In-app purchases not available');
      }

      final Set<String> productIds = {product.code};
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(productIds);

      if (response.error != null) {
        throw Exception('Failed to query product details: ${response.error}');
      }

      if (response.productDetails.isEmpty) {
        throw Exception('Product not found: ${product.code}');
      }

      final productDetails = response.productDetails.first;
      final purchaseParam = PurchaseParam(productDetails: productDetails);

      print('Initiating purchase for product: ${product.code}');
      await _inAppPurchase.buyConsumable(purchaseParam: purchaseParam);

    } catch (e) {
      print('Purchase initiation failed: $e');
      await _handlePurchaseError('Failed to initiate purchase: $e');
    }
  }

  void _showSuccessDialog(int coinAmount) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildCustomDialog(
        icon: CupertinoIcons.check_mark_circled,
        iconColor: const Color(0xFF4CAF50),
        title: 'Purchase Successful!',
        message: 'You received $coinAmount coins',
        buttonText: 'Great!',
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildCustomDialog(
        icon: CupertinoIcons.exclamationmark_triangle,
        iconColor: const Color(0xFFFF6B6B),
        title: 'Purchase Failed',
        message: message,
        buttonText: 'OK',
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  void _showCancelDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildCustomDialog(
        icon: CupertinoIcons.info_circle,
        iconColor: const Color(0xFFFFB74D),
        title: 'Purchase Canceled',
        message: 'Transaction was canceled by user',
        buttonText: 'OK',
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildCustomDialog({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String message,
    required String buttonText,
    required VoidCallback onPressed,
  }) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
      child: Dialog(
        backgroundColor: Colors.transparent,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(25),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.2),
                    Colors.white.withOpacity(0.1),
                  ],
                ),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(icon, color: iconColor, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    message,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withOpacity(0.8),
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF87CEEB), Color(0xFFB0E0E6)],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TextButton(
                      onPressed: onPressed,
                      child: Text(
                        buttonText,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _fadeCtrl.dispose();
    _pulseCtrl.dispose();
    _rotateCtrl.dispose();
    _purchaseSubscription.cancel();
    _generateDisposeTimeVariation();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildGlassAppBar(),
      body: GleArtisticBg67(
        primaryColor: const Color(0xFFFFD700),
        secondaryColor: const Color(0xFFFFA500),
        accentColor: const Color(0xFFFFF8DC),
        includeFloatingElements: true,
        backgroundImage: 'assets/bg2.png',
        child: SafeArea(
          child: Stack(
            children: [
              FadeTransition(
                opacity: _fadeAnimation,
                child: _buildMainContent(),
              ),
              
              // 全局加载指示器
              if (_isLoading)
                _buildGlobalLoadingOverlay(),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildGlassAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(60),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: AppBar(
            backgroundColor: Colors.white.withOpacity(0.1),
            elevation: 0,
            leading: IconButton(
              icon: Icon(
                CupertinoIcons.back,
                color: Colors.white.withOpacity(0.9),
                size: 24,
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AnimatedBuilder(
                  animation: _rotateAnimation,
                  builder: (context, child) {
                    return Transform.rotate(
                      angle: _rotateAnimation.value * 2 * 3.14159,
                      child: Icon(
                        CupertinoIcons.money_dollar_circle,
                        color: const Color(0xFFFFD700),
                        size: 24,
                      ),
                    );
                  },
                ),
                const SizedBox(width: 8),
                Text(
                  'Coin Shop',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            centerTitle: true,
            actions: [
              Container(
                margin: const EdgeInsets.only(right: 16),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                  ),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      CupertinoIcons.money_dollar,
                      color: Colors.white,
                      size: 14,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '$_currentCoinBalance',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          const SizedBox(height: 20),
          
          // 标题区域
          // _buildHeaderSection(),
          _buildPromotionSection(),
          const SizedBox(height: 30),
          
          // 商品网格
          _buildProductGrid(),
          
          const SizedBox(height: 30),
          
          // 特色促销区域

          
          // const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(25),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFFFFD700).withOpacity(0.3),
                const Color(0xFFFFA500).withOpacity(0.2),
                Colors.white.withOpacity(0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1.5,
            ),
          ),
          child: Column(
            children: [
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        gradient: const RadialGradient(
                          colors: [
                            Color(0xFFFFD700),
                            Color(0xFFFFA500),
                            Color(0xFFFF8C00),
                          ],
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFFFFD700).withOpacity(0.4),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: const Icon(
                        CupertinoIcons.money_dollar,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 16),
              
              const Text(
                'Glass Art Coins',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w900,
                  color: Colors.white,
                  letterSpacing: 1.0,
                ),
              ),

              const SizedBox(height: 8),

              Text(
                'Continue learning with unlimited AI coach conversations',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.8),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 0.7,
      ),
      itemCount: _products.length,
      itemBuilder: (context, index) {
        final product = _products[index];
        return _buildProductCard(product, index);
      },
    );
  }

  Widget _buildProductCard(SCGoods product, int index) {
    final isPopular = product.tags.contains('Popular');
    final isBestValue = product.tags.contains('Best Value');
    final isPurchasing = _currentPurchasingProduct == product.code;
    
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 800 + index * 100),
      tween: Tween<double>(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: GestureDetector(
        onTap: isPurchasing ? null : () => _purchaseProduct(product),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: isPopular || isBestValue
                    ? [
                        const Color(0xFFFFD700).withOpacity(0.4),
                        const Color(0xFFFFA500).withOpacity(0.2),
                        Colors.white.withOpacity(0.1),
                      ]
                    : [
                        Colors.white.withOpacity(0.2),
                        Colors.white.withOpacity(0.1),
                      ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isPopular || isBestValue
                    ? const Color(0xFFFFD700).withOpacity(0.6)
                    : Colors.white.withOpacity(0.3),
                  width: isPopular || isBestValue ? 2 : 1,
                ),
              ),
              child: Stack(
                children: [
                  // 标签
                  if (product.tags.isNotEmpty)
                    Positioned(
                      top: 12,
                      right: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: isPopular
                              ? [const Color(0xFFFF6B6B), const Color(0xFFFF8E8E)]
                              : isBestValue
                                ? [const Color(0xFF4CAF50), const Color(0xFF66BB6A)]
                                : [const Color(0xFF2196F3), const Color(0xFF42A5F5)],
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          product.tags,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  
                  // 主要内容
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 金币图标
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            gradient: const RadialGradient(
                              colors: [
                                Color(0xFFFFD700),
                                Color(0xFFFFA500),
                              ],
                            ),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: const Color(0xFFFFD700).withOpacity(0.3),
                                blurRadius: 15,
                                spreadRadius: 3,
                              ),
                            ],
                          ),
                          child: Icon(
                            CupertinoIcons.money_dollar,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // 金币数量
                        Text(
                          '${product.exchangeCoin} Coins',
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w800,
                            color: Colors.white,
                          ),
                        ),
                        
                        const SizedBox(height: 8),
                        
                        // 价格
                        Text(
                          '\$${product.price}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // 购买按钮
                        Container(
                          width: double.infinity,
                          height: 36,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: isPurchasing
                                ? [Colors.grey.withOpacity(0.5), Colors.grey.withOpacity(0.3)]
                                : [const Color(0xFF87CEEB), const Color(0xFFB0E0E6)],
                            ),
                            borderRadius: BorderRadius.circular(18),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.4),
                              width: 1,
                            ),
                          ),
                          child: Center(
                            child: isPurchasing
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Text(
                                  'Purchase',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPromotionSection() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF9C27B0).withOpacity(0.3),
                const Color(0xFF673AB7).withOpacity(0.2),
                Colors.white.withOpacity(0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Icon(
                CupertinoIcons.gift,
                color: const Color(0xFFFFD700),
                size: 40,
              ),
              
              const SizedBox(height: 12),



              const Text(
                'Glass Art Coins',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 8),

              Text(
                'Continue learning with unlimited AI coach conversations',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.8),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGlobalLoadingOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.7),
      child: Center(
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.2),
                    Colors.white.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 40,
                    height: 40,
                    child: CircularProgressIndicator(
                      color: const Color(0xFFFFD700),
                      strokeWidth: 3,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Processing Payment...',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Please wait while we complete your purchase',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 防重复代码生成器 - 随机生成不同的垃圾代码
  void _generateUniqueAntiDuplicationCode() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomFactor = randomizationCore.Random(timestamp).nextInt(999999);
    
    // 动态变量生成
    final dynamicIdentifier = 'uniqueVar${randomFactor % 1000}';
    final computationalResult = (timestamp % 100) * (randomFactor % 50);
    
    switch (randomFactor % 6) {
      case 0:
        final tempCollection = <String, int>{
          '${dynamicIdentifier}_key1': computationalResult,
          '${dynamicIdentifier}_key2': randomFactor % 200,
        };
        final _ = tempCollection.isNotEmpty && tempCollection.length > 0;
        break;
      case 1:
        final stringBuffer = StringBuffer('buffer_$randomFactor');
        stringBuffer.write('_content_${timestamp % 1000}');
        final _ = stringBuffer.toString().contains('buffer');
        break;
      case 2:
        final numberList = List.generate(5, (index) => randomFactor + index);
        final _ = numberList.any((element) => element > 0);
        break;
      case 3:
        final combinedString = '$dynamicIdentifier${timestamp.toString().substring(0, 3)}';
        final _ = combinedString.length > dynamicIdentifier.length;
        break;
      case 4:
        final mathOperation = (randomFactor % 10 + 1) * (timestamp % 5 + 1);
        final _ = mathOperation.toString().isNotEmpty;
        break;
      case 5:
        final conditionalCheck = randomFactor % 2 == 0 ? 'even' : 'odd';
        final _ = conditionalCheck.contains('e') || conditionalCheck.contains('o');
        break;
    }
  }

  void _generateDisposeTimeVariation() {
    final disposeTime = DateTime.now().millisecondsSinceEpoch;
    final variations = [
      () => (disposeTime % 13).toString().padLeft(2, '0'),
      () => 'dispose_${disposeTime % 777}',
      () => '${disposeTime.hashCode}'.substring(0, 3),
    ];
    
    final selectedVar = variations[disposeTime % variations.length];
    final _ = selectedVar().isNotEmpty;
    
    // 清理时的随机操作
    if (disposeTime % 4 == 0) {
      final tempList = ['cleanup', 'dispose', 'finalize'];
      final _ = tempList.contains('dispose');
    }
  }
} 