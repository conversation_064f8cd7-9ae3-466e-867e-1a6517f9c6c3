import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'dart:math' as math;
import '../CulletParts/GleArtisticBg67.dart';
import '../FrostAtelier/GleMainVi78.dart';
import '../PrismVault/GleFavoriteDataMgr67.dart';
import 'GleChatVi45.dart';

/// Glemi AI教练角色详情页面
/// 玻璃拟态设计的教练详情展示和聊天入口
class GleCoachDetai88Vi extends StatefulWidget {
  final AiCoach34Model coach;
  
  const GleCoachDetai88Vi({
    super.key,
    required this.coach,
  });

  @override
  State<GleCoachDetai88Vi> createState() => _GleCoachDetai88ViState();
}

class _GleCoachDetai88ViState extends State<GleCoachDetai88Vi>
    with TickerProviderStateMixin {

  late AnimationController _fadeCtrl;
  late AnimationController _scaleCtrl;
  late AnimationController _shimmerCtrl;

  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shimmerAnimation;

  // 收藏管理器和状态
  final GleFavoriteDataMgr67 _favoriteManager = GleFavoriteDataMgr67();
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _initFavoriteStatus();
    _startAnimations();
  }

  /// 初始化收藏状态
  void _initFavoriteStatus() {
    _isFavorite = _favoriteManager.isFavorite(widget.coach.name);

    // 监听收藏状态变化
    _favoriteManager.favoritesStream.listen((favorites) {
      if (mounted) {
        setState(() {
          _isFavorite = favorites.contains(widget.coach.name);
        });
      }
    });
  }

  void _initAnimations() {
    _fadeCtrl = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _scaleCtrl = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _shimmerCtrl = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeCtrl,
      curve: Curves.easeOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleCtrl,
      curve: Curves.elasticOut,
    ));
    
    _shimmerAnimation = Tween<double>(
      begin: -1.5,
      end: 1.5,
    ).animate(CurvedAnimation(
      parent: _shimmerCtrl,
      curve: Curves.easeInOutSine,
    ));
  }

  void _startAnimations() {
    _fadeCtrl.forward();
    _scaleCtrl.forward();
    _shimmerCtrl.repeat(reverse: true);
  }

  @override
  void dispose() {
    _fadeCtrl.dispose();
    _scaleCtrl.dispose();
    _shimmerCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildGlassAppBar(),
      body: GleArtisticBg67(
        primaryColor: widget.coach.glassColor,
        secondaryColor: widget.coach.accentColor,
        accentColor: const Color(0xFFFFF8DC),
        includeFloatingElements: true,
        backgroundImage: 'assets/bg2.png', // 添加背景图片
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    const SizedBox(height: 20),
                    
                    // 主要角色卡片
                    _buildMainCoachCard(),
                    
                    const SizedBox(height: 20),
                    
                    // 聊天按钮 - 移动到这里
                    _buildChatButton(),
                    
                    const SizedBox(height: 30),
                    
                    // 技能特长区域
                    _buildSkillsSection(),
                    
                    const SizedBox(height: 30),
                    
                    // 详细描述区域
                    _buildDescriptionSection(),
                    
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建玻璃拟态AppBar
  PreferredSizeWidget _buildGlassAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(60),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: AppBar(
            backgroundColor: Colors.white.withOpacity(0.1),
            elevation: 0,
            leading: IconButton(
              icon: Icon(
                CupertinoIcons.back,
                color: Colors.white.withOpacity(0.9),
                size: 24,
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
            title: Text(
              'Coach Profile',
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            centerTitle: true,
            actions: [
              // 收藏按钮
              IconButton(
                icon: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: Icon(
                    _isFavorite ? CupertinoIcons.heart_fill : CupertinoIcons.heart,
                    key: ValueKey(_isFavorite),
                    color: _isFavorite ? Colors.red.withOpacity(0.9) : Colors.white.withOpacity(0.9),
                    size: 24,
                  ),
                ),
                onPressed: _toggleFavorite,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建主要角色卡片
  Widget _buildMainCoachCard() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(30),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
        child: Container(
          padding: const EdgeInsets.all(28),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                widget.coach.glassColor.withOpacity(0.3),
                widget.coach.accentColor.withOpacity(0.15),
                Colors.white.withOpacity(0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(
              color: widget.coach.glassColor.withOpacity(0.4),
              width: 2,
            ),
          ),
          child: Stack(
            children: [
              // 霓虹效果
              _buildDetailShimmer(),
              
              Column(
                children: [
                  // 大头像
                  _buildLargeAvatar(),
                  
                  const SizedBox(height: 24),
                  
                  // 专业标签
                  _buildSpecialtyBadge(),
                  
                  const SizedBox(height: 16),
                  
                  // 角色名称
                  Text(
                    widget.coach.displayName,
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.w900,
                      color: Colors.white,
                      letterSpacing: 1.0,
                      height: 1.1,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // 基础描述
                  Text(
                    widget.coach.description,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withOpacity(0.85),
                      height: 1.4,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建详情页霓虹效果
  Widget _buildDetailShimmer() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              gradient: LinearGradient(
                begin: Alignment(-1.5 + _shimmerAnimation.value, -1.0),
                end: Alignment(1.5 + _shimmerAnimation.value, 1.0),
                colors: [
                  Colors.transparent,
                  widget.coach.glassColor.withOpacity(0.1),
                  widget.coach.accentColor.withOpacity(0.08),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.4, 0.6, 1.0],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建大头像
  Widget _buildLargeAvatar() {
    return Container(
      width: 160,
      height: 160,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            widget.coach.glassColor.withOpacity(0.4),
            widget.coach.accentColor.withOpacity(0.2),
            Colors.transparent,
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
        boxShadow: [
          BoxShadow(
            color: widget.coach.glassColor.withOpacity(0.4),
            blurRadius: 30,
            spreadRadius: 10,
          ),
          BoxShadow(
            color: widget.coach.accentColor.withOpacity(0.3),
            blurRadius: 50,
            spreadRadius: 20,
          ),
        ],
      ),
      child: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white.withOpacity(0.4),
            width: 3,
          ),
        ),
        child: ClipOval(
          child: Image.asset(
            widget.coach.imagePath,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      widget.coach.glassColor,
                      widget.coach.accentColor,
                    ],
                  ),
                ),
                child: Icon(
                  CupertinoIcons.person_circle_fill,
                  size: 100,
                  color: Colors.white.withOpacity(0.8),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 构建专业标签
  Widget _buildSpecialtyBadge() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(25),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                widget.coach.glassColor.withOpacity(0.5),
                widget.coach.accentColor.withOpacity(0.3),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: Colors.white.withOpacity(0.4),
              width: 1.5,
            ),
          ),
          child: Text(
            widget.coach.specialty,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white,
              fontWeight: FontWeight.w700,
              letterSpacing: 1.0,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建技能特长区域
  Widget _buildSkillsSection() {
    return _buildGlassSection(
      title: 'Expertise',
      child: Column(
        children: [
          _buildSkillItem('Glass Painting Techniques', 95),
          const SizedBox(height: 16),
          _buildSkillItem('Color Theory & Mixing', 88),
          const SizedBox(height: 16),
          _buildSkillItem('Creative Guidance', 92),
        ],
      ),
    );
  }

  /// 构建技能条目
  Widget _buildSkillItem(String skill, int percentage) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              skill,
              style: TextStyle(
                fontSize: 15,
                color: Colors.white.withOpacity(0.9),
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '$percentage%',
              style: TextStyle(
                fontSize: 14,
                color: widget.coach.glassColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
            height: 6,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: percentage / 100,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      widget.coach.glassColor,
                      widget.coach.accentColor,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建详细描述区域
  Widget _buildDescriptionSection() {
    return _buildGlassSection(
      title: 'About This Coach',
      child: Text(
        'As your dedicated ${widget.coach.displayName}, I specialize in ${widget.coach.specialty.toLowerCase()} with years of experience in glass art creation. My approach combines traditional techniques with modern innovation, ensuring you master both fundamental skills and creative expression. Whether you\'re a beginner or looking to refine your technique, I\'m here to guide you through every step of your glass painting journey.',
        style: TextStyle(
          fontSize: 16,
          color: Colors.white.withOpacity(0.85),
          height: 1.5,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  /// 构建聊天按钮
  Widget _buildChatButton() {
    return GestureDetector(
      onTap: _onStartChat,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: Container(
            width: double.infinity,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  widget.coach.glassColor.withOpacity(0.6),
                  widget.coach.accentColor.withOpacity(0.4),
                ],
              ),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: Colors.white.withOpacity(0.4),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: widget.coach.glassColor.withOpacity(0.4),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
                BoxShadow(
                  color: widget.coach.accentColor.withOpacity(0.3),
                  blurRadius: 30,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    CupertinoIcons.chat_bubble_2,
                    color: Colors.white,
                    size: 22,
                  ),
                ),
                const SizedBox(width: 16),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Start Chat',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                        letterSpacing: 0.5,
                      ),
                    ),
                    Text(
                      'Begin your glass painting journey',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.8),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Icon(
                  CupertinoIcons.arrow_right_circle_fill,
                  color: Colors.white.withOpacity(0.8),
                  size: 24,
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建玻璃拟态区域组件
  Widget _buildGlassSection({required String title, required Widget child}) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(25),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(0.15),
                Colors.white.withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                  letterSpacing: 0.5,
                ),
              ),
              const SizedBox(height: 16),
              child,
            ],
          ),
        ),
      ),
    );
  }

  /// 切换收藏状态
  void _toggleFavorite() async {
    await _favoriteManager.toggleFavorite(widget.coach.name);

    // 显示收藏状态提示
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _isFavorite
              ? '❤️ Added ${widget.coach.displayName} to favorites'
              : '💔 Removed ${widget.coach.displayName} from favorites',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: _isFavorite
            ? Colors.red.withOpacity(0.8)
            : Colors.grey.withOpacity(0.8),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  /// 开始聊天事件
  void _onStartChat() {
    print('🎨 Starting chat with: ${widget.coach.displayName}');
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => GleChatVi45(coach: widget.coach),
      ),
    );
  }
} 