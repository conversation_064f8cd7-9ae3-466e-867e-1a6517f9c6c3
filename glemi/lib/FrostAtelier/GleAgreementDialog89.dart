import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'package:shared_preferences/shared_preferences.dart';

/// Glemi用户协议和隐私协议弹窗
class GleAgreementDialog89 extends StatefulWidget {
  final VoidCallback onAccepted;
  final VoidCallback onRejected;

  const GleAgreementDialog89({
    super.key,
    required this.onAccepted,
    required this.onRejected,
  });

  /// 检查用户是否已同意协议
  static Future<bool> hasUserAcceptedAgreement() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('user_agreement_accepted') ?? false;
  }

  @override
  State<GleAgreementDialog89> createState() => _GleAgreementDialog89State();
}

class _GleAgreementDialog89State extends State<GleAgreementDialog89>
    with TickerProviderStateMixin {
  
  late AnimationController _fadeCtrl;
  late AnimationController _scaleCtrl;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  bool _showPrivacyPolicy = true; // true: 隐私协议, false: 用户协议
  
  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startAnimations();
  }

  void _initAnimations() {
    _fadeCtrl = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _scaleCtrl = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeCtrl,
      curve: Curves.easeOutQuart,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleCtrl,
      curve: Curves.elasticOut,
    ));
  }

  void _startAnimations() {
    _fadeCtrl.forward();
    _scaleCtrl.forward();
  }

  @override
  void dispose() {
    _fadeCtrl.dispose();
    _scaleCtrl.dispose();
    super.dispose();
  }

  /// 保存用户同意状态
  Future<void> _saveAgreementAccepted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('user_agreement_accepted', true);
    await prefs.setString('agreement_accepted_date', DateTime.now().toIso8601String());
  }

  /// 处理同意按钮
  void _handleAccept() async {
    await _saveAgreementAccepted();
    widget.onAccepted();
  }

  /// 处理拒绝按钮
  void _handleReject() {
    widget.onRejected();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withOpacity(0.7),
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Center(
            child: Container(
              margin: const EdgeInsets.all(20),
              constraints: const BoxConstraints(
                maxWidth: 400,
                maxHeight: 600,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(25),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.white.withOpacity(0.2),
                          Colors.white.withOpacity(0.1),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        // 标题和切换区域
                        _buildHeader(),
                        
                        // 协议内容区域
                        Expanded(
                          child: _buildContentArea(),
                        ),
                        
                        // 按钮区域
                        _buildButtonArea(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建标题和切换区域
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // 主标题
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                CupertinoIcons.doc_text,
                color: const Color(0xFF87CEEB),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Terms & Privacy',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 切换按钮
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // 隐私协议按钮
                Expanded(
                  child: GestureDetector(
                    onTap: () => setState(() => _showPrivacyPolicy = true),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: _showPrivacyPolicy 
                          ? const Color(0xFF87CEEB).withOpacity(0.3)
                          : Colors.transparent,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        'Privacy Policy',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: _showPrivacyPolicy 
                            ? Colors.white
                            : Colors.white.withOpacity(0.7),
                          fontSize: 14,
                          fontWeight: _showPrivacyPolicy 
                            ? FontWeight.w600 
                            : FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
                
                // 用户协议按钮
                Expanded(
                  child: GestureDetector(
                    onTap: () => setState(() => _showPrivacyPolicy = false),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: !_showPrivacyPolicy 
                          ? const Color(0xFF87CEEB).withOpacity(0.3)
                          : Colors.transparent,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        'User Agreement',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: !_showPrivacyPolicy 
                            ? Colors.white
                            : Colors.white.withOpacity(0.7),
                          fontSize: 14,
                          fontWeight: !_showPrivacyPolicy 
                            ? FontWeight.w600 
                            : FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContentArea() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.2),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Text(
          _showPrivacyPolicy ? _getPrivacyPolicyText() : _getUserAgreementText(),
          style: TextStyle(
            color: Colors.white.withOpacity(0.9),
            fontSize: 13,
            height: 1.5,
            fontFamily: 'monospace',
          ),
        ),
      ),
    );
  }

  /// 构建按钮区域
  Widget _buildButtonArea() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // 拒绝按钮
          Expanded(
            child: GestureDetector(
              onTap: _handleReject,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 14),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.red.withOpacity(0.4),
                    width: 1,
                  ),
                ),
                child: Text(
                  'Decline',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.red.withOpacity(0.9),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 同意按钮
          Expanded(
            child: GestureDetector(
              onTap: _handleAccept,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 14),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFF87CEEB),
                      const Color(0xFF4682B4),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF87CEEB).withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Text(
                  'Accept',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取隐私协议文本
  String _getPrivacyPolicyText() {
    return '''Privacy Policy for Glemi
Last Updated: July 29, 2025
Contact: <EMAIL>

1. Introduction
Welcome to Glemi, your AI-powered glass painting companion. We take your privacy seriously. This Privacy Policy outlines what data we collect, how we use it, and the steps we take to protect your information. By using Glemi, you agree to the practices described here.

2. Scope of This Policy
This policy applies exclusively to the Glemi app, which provides AI-driven dialogue tools to assist with glass painting. It covers all interactions within the app, including conversations with AI characters, local storage of user data, and optional user customization.

3. Information We Collect
3.1. Local Data Storage (No Server Upload)
We prioritize your privacy by storing the following data only on your device:
• Chat History: Conversations with AI characters are saved locally via SQLite. You can view, delete, or clear these records at any time.
• User Profile Data: Your chosen avatar and nickname are stored locally.
• Favorites: Your list of preferred AI characters is saved for easier access.

No personal data or content is transmitted to external servers. Glemi does not collect or upload your information to a cloud database.

4. How We Use Your Information
Your data is used solely to enable key app functionalities:
• Display and manage chat history
• Monitor remaining free chat interactions
• Customize your experience (e.g., avatar, nickname)
• Show motivational quotes ("Daily Quote") when the app launches
• Offer a seamless user interface when navigating through characters and conversations

Glemi does not engage in user profiling, targeted advertising, or behavioral tracking.

5. AI Chat Interactions
Each of Glemi's ten AI characters responds based on unique artistic roles. These responses are generated using the Moonshot text generation model, tailored by your input.

6. Free Usage and Message Tracking
• Each AI character includes 3 free messages per user.
• Message counts are tracked and stored locally.
• Once the quota is exceeded, users are notified that the chat cannot continue unless you unlock more chat sessions by using gold coins.

This functionality does not require identity verification or personal user tracking.

7. User Control and Data Management
Glemi empowers users with full control over their data:
• Delete Specific Chats: Remove any single conversation manually.
• Clear Chat History: Wipe all past conversations with a single action.
• Edit or Remove Profile Info: Change or delete your avatar and nickname at any time.

All actions are executed locally and take immediate effect.

8. Security Measures
To ensure data confidentiality:
• All user data is stored in a protected local database (SQLite).
• No cloud syncing is used, eliminating the risk of data leakage via remote servers.
• The app does not require login, thereby avoiding the collection of email addresses, passwords, or identifiers.

9. Children's Privacy
Glemi is not directed toward users under 13 years of age. We do not knowingly collect personal information from minors. If you believe a child has used Glemi and submitted personal data, please contact us to initiate deletion procedures.

10. Third-Party Services
Glemi does not integrate with third-party SDKs or analytics services. No third-party advertising, data brokers, or tracking libraries are used within the app.

11. Updates to This Policy
This Privacy Policy may be updated to reflect new features or regulatory requirements. Users will be notified within the app of material changes. Continued use of Glemi after updates constitutes acceptance of the revised policy.

12. Contact Us
If you have any questions, feedback, or concerns about your privacy or this policy, please contact us at:
📩 <EMAIL>''';
  }

  /// 获取用户协议文本
  String _getUserAgreementText() {
    return '''User Agreement for Glemi
Last Updated: July 29, 2025
Contact: <EMAIL>

1. Introduction and Acceptance
Welcome to Glemi, a digital assistant designed to guide users in the art of glass painting through AI-driven dialogue. By accessing or using Glemi ("the App"), you acknowledge and agree to comply with the terms laid out in this User Agreement ("Agreement"). If you disagree with any part of this Agreement, you must refrain from using the App.

This Agreement establishes the rights and responsibilities between you ("you" or "the user") and Glemi's development team ("we", "our", or "Glemi").

2. Intended Use and Audience
Glemi is a creative tool built for artists, hobbyists, and beginners interested in decorative glass painting. The application offers character-driven artistic coaching through a chat interface powered by generative AI.

Use of this App is permitted for individuals aged 13 and above. If you are under the age of majority in your region, you must use this service under supervision of a legal guardian who agrees to the terms herein.

3. Key Features and Service Scope
Glemi provides non-commercial artistic support through features such as:
• AI-generated responses tailored to glass art styles
• Daily motivational quotes shown on launch
• Ten distinct AI personas, each specializing in techniques such as color blending, line drawing, pattern design, and mistake correction
• Local chat history and personalized user settings

The App does not guarantee artistic results, and all advice should be interpreted as creative suggestions rather than professional instruction.

4. User Identity and Personalization
Glemi allows you to personalize your experience by selecting avatars, nicknames, and marking favorite AI guides. This customization is optional and solely for your benefit. No account creation or login is required. Your identity is never connected to a server, and all personalization remains local to your device.

5. Interaction Rules and Message Limits
To ensure fair usage, Glemi applies default limitations on free interactions:
• Each AI guide provides 3 free conversations
• Every message sent reduces the remaining count by 1
• Once the limit is reached, users are notified and cannot proceed unless unlock more chat sessions by using gold coins

6. Generated Content and Creative Use
All AI-generated responses in Glemi are intended to serve as inspiration for your personal glass art. While the guidance is customized based on each AI persona's skillset, the outcomes are interpretative and not factual or prescriptive.

Users are free to apply this guidance in personal artwork. However, use of AI-generated content in commercial publications, marketplaces, or tutorials must include your own transformation and effort.

7. Local Data and User Control
Glemi does not collect or transmit any user data to remote servers. All of your information—including message history, favorites, and custom profile elements—is stored locally on your device via embedded databases (e.g., SQLite).

You may:
• Review past conversations in the "Chat History" section
• Delete individual chats or clear all records manually
• Manage personal settings at your discretion

We are not liable for any data lost due to app uninstallation or device failure.

8. Daily Quotes Feature
Upon launching the app, a brief motivational quote may appear under the main title banner. This feature is designed for artistic encouragement and can be dismissed at any time. Quotes do not contain personalized tracking or analytics.

9. Intellectual Ownership and Usage Guidelines
The app and all related materials—including UI design, AI personas, descriptions, and training logic—are the intellectual property of Glemi and its licensors.

You may not:
• Copy, distribute, or reverse-engineer any part of the App
• Misrepresent AI outputs as human-authored professional advice
• Use the content for public instruction or resell guidance without transformation
• Modify or manipulate AI behavior through external tools or unauthorized methods

Misuse may result in restricted access to features or termination of your ability to use the App.

10. System Updates and App Evolution
We may periodically update Glemi to introduce improvements, bug fixes, or content changes. You are encouraged to install the latest version for an optimal experience. Older versions may lose functionality or compatibility without notice.

Major updates to this Agreement will be presented within the app for review.

11. Suspension and User Conduct
We reserve the right to disable app features temporarily or permanently if misuse, abuse of the interaction limits, or violation of creative conduct is detected. Respectful engagement with AI content is expected at all times.

12. Contact and Feedback
We welcome your suggestions, error reports, or creative ideas. You may contact us directly at:
Email: <EMAIL>

By continuing to use Glemi, you confirm that you have read, understood, and agreed to be bound by this Agreement.''';
  }
}
