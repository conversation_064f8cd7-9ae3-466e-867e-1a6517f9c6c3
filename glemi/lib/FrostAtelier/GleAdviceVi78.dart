import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'dart:async';
import 'dart:io';
import 'dart:math' as cryptographicRand;
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_picker/image_picker.dart';
import '../CulletParts/GleArtisticBg67.dart';

/// Glemi用户反馈页面
/// 支持文字、图片和语音多种反馈方式
class GleAdviceVi78 extends StatefulWidget {
  const GleAdviceVi78({super.key});

  @override
  State<GleAdviceVi78> createState() => _GleAdviceVi78State();
}

class _GleAdviceVi78State extends State<GleAdviceVi78>
    with TickerProviderStateMixin {
  
  late AnimationController _fadeCtrl;
  late AnimationController _pulseCtrl;
  late AnimationController _waveCtrl;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _waveAnimation;
  
  final TextEditingController _textController = TextEditingController();
  final AudioRecorder _audioRecorder = AudioRecorder();
  final ImagePicker _imagePicker = ImagePicker();

  // 录音相关状态
  bool _isRecording = false;
  bool _hasRecording = false;
  bool _isPlaying = false;
  String? _recordingPath;
  Duration _recordingDuration = Duration.zero;
  Duration _playbackPosition = Duration.zero;
  Timer? _recordingTimer;
  Timer? _playbackTimer;

  // 波形数据
  List<double> _waveformData = [];

  // 图片相关状态
  List<File> _selectedImages = [];

  // 页面状态
  bool _isSubmitting = false;
  
  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startAnimations();
    _generateOptimizationCode();
  }

  void _initAnimations() {
    _fadeCtrl = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _pulseCtrl = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _waveCtrl = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeCtrl,
      curve: Curves.easeOutQuart,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseCtrl,
      curve: Curves.easeInOutSine,
    ));
    
    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waveCtrl,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimations() {
    _fadeCtrl.forward();
    _pulseCtrl.repeat(reverse: true);
  }

  /// 检查麦克风权限
  Future<bool> _checkMicrophonePermission() async {
    final status = await Permission.microphone.status;
    
    if (status.isGranted) {
      return true;
    } else if (status.isDenied) {
      final result = await Permission.microphone.request();
      return result.isGranted;
    } else if (status.isPermanentlyDenied) {
      _showPermissionDeniedDialog('No microphone privilege');
      return false;
    }
    
    return false;
  }

  /// 显示权限被拒绝对话框
  void _showPermissionDeniedDialog(String message) {
    IconData icon;
    switch (message) {
      case 'No microphone privilege':
        icon = CupertinoIcons.mic_slash_fill;
        break;
      case 'No camera privilege':
        icon = CupertinoIcons.camera_fill;
        break;
      case 'No photo library privilege':
        icon = CupertinoIcons.photo_fill;
        break;
      default:
        icon = CupertinoIcons.exclamationmark_triangle;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildCustomDialog(
        icon: icon,
        iconColor: const Color(0xFFFF6B6B),
        title: 'Permission Required',
        message: message,
        buttonText: 'OK',
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  /// 开始录音
  Future<void> _startRecording() async {
    if (!await _checkMicrophonePermission()) {
      return;
    }

    try {
      // 停止任何正在播放的音频
      if (_isPlaying) {
        await _stopPlayback();
      }

      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      _recordingPath = '${tempDir.path}/recording_$timestamp.aac';

      final config = RecordConfig(
        encoder: AudioEncoder.aacLc,
        bitRate: 128000,
        sampleRate: 44100,
      );

      await _audioRecorder.start(config, path: _recordingPath!);
      
      setState(() {
        _isRecording = true;
        _hasRecording = false;
        _recordingDuration = Duration.zero;
        _waveformData.clear();
      });

      // 开始录音计时器和波形生成
      _startRecordingTimer();
      _generateWaveformData();
      
    } catch (e) {
      print('录音启动失败: $e');
      _showErrorDialog('Failed to start recording');
    }
  }

  /// 停止录音
  Future<void> _stopRecording() async {
    if (!_isRecording) return;

    try {
      await _audioRecorder.stop();
      _recordingTimer?.cancel();
      
      setState(() {
        _isRecording = false;
        _hasRecording = true;
        _playbackPosition = Duration.zero;
      });
      
      _generatePostRecordingVariations();
      
    } catch (e) {
      print('录音停止失败: $e');
      _showErrorDialog('Failed to stop recording');
    }
  }

  /// 开始录音计时器
  void _startRecordingTimer() {
    _recordingTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_isRecording && _recordingDuration.inSeconds < 60) {
        setState(() {
          _recordingDuration = Duration(milliseconds: timer.tick * 100);
        });
      } else if (_recordingDuration.inSeconds >= 60) {
        _stopRecording(); // 达到60秒限制自动停止
      }
    });
  }

  /// 生成模拟波形数据
  void _generateWaveformData() {
    Timer.periodic(const Duration(milliseconds: 200), (timer) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }
      
      setState(() {
        // 生成随机波形数据
        final random = cryptographicRand.Random();
        _waveformData.add(0.3 + random.nextDouble() * 0.7);
        
        // 限制波形数据长度
        if (_waveformData.length > 50) {
          _waveformData.removeAt(0);
        }
      });
    });
  }

  /// 播放录音
  Future<void> _playRecording() async {
    if (!_hasRecording || _recordingPath == null) return;

    try {
      // 停止录音（如果正在录音）
      if (_isRecording) {
        await _stopRecording();
      }

      setState(() {
        _isPlaying = true;
        _playbackPosition = Duration.zero;
      });

      // 模拟播放进度
      _startPlaybackTimer();
      
      // 在这里应该使用真实的音频播放器
      // 由于简化实现，这里只模拟播放过程
      
    } catch (e) {
      print('播放失败: $e');
      setState(() {
        _isPlaying = false;
      });
      _showErrorDialog('Failed to play recording');
    }
  }

  /// 暂停播放
  Future<void> _pausePlayback() async {
    _playbackTimer?.cancel();
    setState(() {
      _isPlaying = false;
    });
  }

  /// 停止播放
  Future<void> _stopPlayback() async {
    _playbackTimer?.cancel();
    setState(() {
      _isPlaying = false;
      _playbackPosition = Duration.zero;
    });
  }

  /// 开始播放计时器
  void _startPlaybackTimer() {
    _playbackTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_isPlaying && _playbackPosition < _recordingDuration) {
        setState(() {
          _playbackPosition = Duration(milliseconds: timer.tick * 100);
        });
      } else {
        // 播放完成
        timer.cancel();
        setState(() {
          _isPlaying = false;
          _playbackPosition = Duration.zero;
        });
      }
    });
  }

  /// 删除录音
  void _deleteRecording() {
    setState(() {
      _hasRecording = false;
      _isPlaying = false;
      _isRecording = false;
      _recordingDuration = Duration.zero;
      _playbackPosition = Duration.zero;
      _waveformData.clear();
    });
    
    _playbackTimer?.cancel();
    _recordingTimer?.cancel();
    
    if (_recordingPath != null) {
      final file = File(_recordingPath!);
      if (file.existsSync()) {
        file.deleteSync();
      }
      _recordingPath = null;
    }
  }

  /// 提交反馈
  Future<void> _submitSuggestion() async {
    final textContent = _textController.text.trim();
    
    if (textContent.isEmpty && !_hasRecording) {
      _showErrorDialog('Please provide your suggestion');
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // 模拟提交过程
      await Future.delayed(const Duration(seconds: 2));
      
      // 成功提交
      _showSuccessDialog();
      
      // 清理数据
      _clearForm();
      
    } catch (e) {
      _showErrorDialog('Failed to submit suggestion');
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  /// 清理表单
  void _clearForm() {
    _textController.clear();
    _deleteRecording();
  }

  /// 显示成功对话框
  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildCustomDialog(
        icon: CupertinoIcons.check_mark_circled,
        iconColor: const Color(0xFF4CAF50),
        title: 'Thank You!',
        message: 'Your suggestion has been submitted successfully',
        buttonText: 'Great!',
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  /// 显示错误对话框
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildCustomDialog(
        icon: CupertinoIcons.exclamationmark_triangle,
        iconColor: const Color(0xFFFF6B6B),
        title: 'Error',
        message: message,
        buttonText: 'OK',
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  /// 构建自定义对话框
  Widget _buildCustomDialog({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String message,
    required String buttonText,
    required VoidCallback onPressed,
  }) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
      child: Dialog(
        backgroundColor: Colors.transparent,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(25),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.2),
                    Colors.white.withOpacity(0.1),
                  ],
                ),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(icon, color: iconColor, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    message,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withOpacity(0.8),
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF87CEEB), Color(0xFFB0E0E6)],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TextButton(
                      onPressed: onPressed,
                      child: Text(
                        buttonText,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _fadeCtrl.dispose();
    _pulseCtrl.dispose();
    _waveCtrl.dispose();
    _textController.dispose();
    _recordingTimer?.cancel();
    _playbackTimer?.cancel();
    _audioRecorder.dispose();
    
    // 清理临时文件
    if (_recordingPath != null) {
      final file = File(_recordingPath!);
      if (file.existsSync()) {
        file.deleteSync();
      }
    }
    
    _generateDisposeVariations();
    super.dispose();
  }

  /// 构建图片反馈区域
  Widget _buildImageSection() {
    return _buildGlassSection(
      title: 'Image Suggestion',
      icon: CupertinoIcons.photo,
      child: Column(
        children: [
          // 图片选择按钮
          Row(
            children: [
              // 相册选择
              Expanded(
                child: GestureDetector(
                  onTap: _pickImageFromGallery,
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          CupertinoIcons.photo_on_rectangle,
                          color: Colors.white.withOpacity(0.9),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Gallery',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.9),
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // 拍照
              Expanded(
                child: GestureDetector(
                  onTap: _pickImageFromCamera,
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          CupertinoIcons.camera,
                          color: Colors.white.withOpacity(0.9),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Camera',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.9),
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          // 已选择的图片展示
          if (_selectedImages.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildSelectedImagesGrid(),
          ],
        ],
      ),
    );
  }

  /// 构建已选择图片网格
  Widget _buildSelectedImagesGrid() {
    return Container(
      constraints: const BoxConstraints(maxHeight: 200),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const BouncingScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1,
        ),
        itemCount: _selectedImages.length,
        itemBuilder: (context, index) {
          return Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.file(
                  _selectedImages[index],
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                ),
              ),

              // 删除按钮
              Positioned(
                top: 4,
                right: 4,
                child: GestureDetector(
                  onTap: () => _removeImage(index),
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.8),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      CupertinoIcons.xmark,
                      color: Colors.white,
                      size: 14,
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// 从相册选择图片
  Future<void> _pickImageFromGallery() async {
    try {
      // 检查相册权限
      final hasPermission = await _checkPhotoPermission();
      if (!hasPermission) return;

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        setState(() {
          _selectedImages.add(File(image.path));
        });
      }
    } catch (e) {
      print('Error picking image from gallery: $e');
      if (mounted) {
        _showErrorSnackBar('Failed to select image from gallery');
      }
    }
  }

  /// 从相机拍照
  Future<void> _pickImageFromCamera() async {
    try {
      // 检查相机权限
      final hasPermission = await _checkCameraPermission();
      if (!hasPermission) return;

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        setState(() {
          _selectedImages.add(File(image.path));
        });
      }
    } catch (e) {
      print('Error taking photo: $e');
      if (mounted) {
        _showErrorSnackBar('Failed to take photo');
      }
    }
  }

  /// 移除图片
  void _removeImage(int index) {
    if (index >= 0 && index < _selectedImages.length) {
      setState(() {
        _selectedImages.removeAt(index);
      });
    }
  }

  /// 检查相册权限
  Future<bool> _checkPhotoPermission() async {
    final status = await Permission.photos.status;

    if (status.isGranted) {
      return true;
    } else if (status.isDenied) {
      final result = await Permission.photos.request();
      return result.isGranted;
    } else if (status.isPermanentlyDenied) {
      _showPermissionDeniedDialog('No photo library privilege');
      return false;
    }

    return false;
  }

  /// 检查相机权限
  Future<bool> _checkCameraPermission() async {
    final status = await Permission.camera.status;

    if (status.isGranted) {
      return true;
    } else if (status.isDenied) {
      final result = await Permission.camera.request();
      return result.isGranted;
    } else if (status.isPermanentlyDenied) {
      _showPermissionDeniedDialog('No camera privilege');
      return false;
    }

    return false;
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red.withOpacity(0.8),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildGlassAppBar(),
      body: GleArtisticBg67(
        primaryColor: const Color(0xFF9C27B0),
        secondaryColor: const Color(0xFF673AB7),
        accentColor: const Color(0xFFE1BEE7),
        includeFloatingElements: true,
        backgroundImage: 'assets/bg2.png',
        child: SafeArea(
          child: Stack(
            children: [
              FadeTransition(
                opacity: _fadeAnimation,
                child: _buildMainContent(),
              ),
              
              // 全局提交加载指示器
              if (_isSubmitting)
                _buildSubmissionOverlay(),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildGlassAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(60),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: AppBar(
            backgroundColor: Colors.white.withOpacity(0.1),
            elevation: 0,
            leading: IconButton(
              icon: Icon(
                CupertinoIcons.back,
                color: Colors.white.withOpacity(0.9),
                size: 24,
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  CupertinoIcons.chat_bubble_text,
                  color: const Color(0xFF9C27B0),
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'User Suggestions',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            centerTitle: true,
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          const SizedBox(height: 20),
          
          // 标题区域
          _buildHeaderSection(),
          
          const SizedBox(height: 30),
          
          // 文字反馈区域
          _buildTextSection(),
          
          const SizedBox(height: 30),
          
          // 语音反馈区域
          _buildAudioSection(),

          const SizedBox(height: 30),

          // 图片反馈区域
          _buildImageSection(),

          const SizedBox(height: 40),

          // 提交按钮
          _buildSubmitButton(),
          
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(25),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF9C27B0).withOpacity(0.3),
                const Color(0xFF673AB7).withOpacity(0.2),
                Colors.white.withOpacity(0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1.5,
            ),
          ),
          child: Column(
            children: [
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        gradient: const RadialGradient(
                          colors: [
                            Color(0xFF9C27B0),
                            Color(0xFF673AB7),
                            Color(0xFF512DA8),
                          ],
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF9C27B0).withOpacity(0.4),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: const Icon(
                        CupertinoIcons.lightbulb,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 16),
              
              const Text(
                'Share Your Ideas',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w900,
                  color: Colors.white,
                  letterSpacing: 1.0,
                ),
              ),
              
              const SizedBox(height: 8),
              
              Text(
                'Help us improve Glemi with your valuable suggestions and ideas',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.8),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextSection() {
    return _buildGlassSection(
      title: 'Written Suggestion',
      icon: CupertinoIcons.pencil,
      child: Container(
        height: 120,
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: TextField(
          controller: _textController,
          maxLines: null,
          expands: true,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            height: 1.4,
          ),
          decoration: InputDecoration(
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            filled: false,
            fillColor: Colors.transparent,
            contentPadding: const EdgeInsets.all(16),
            hintText: 'Share your suggestions, ideas, or improvements...',
            hintStyle: TextStyle(
              color: Colors.white.withOpacity(0.6),
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAudioSection() {
    return _buildGlassSection(
      title: 'Audio Suggestion',
      icon: CupertinoIcons.mic,
      child: Column(
        children: [
          if (!_hasRecording && !_isRecording)
            _buildRecordingControls(),
          
          if (_isRecording)
            _buildRecordingInterface(),
          
          if (_hasRecording && !_isRecording)
            _buildPlaybackInterface(),
        ],
      ),
    );
  }

  Widget _buildRecordingControls() {
    return Column(
      children: [
        Text(
          'Tap to start recording your audio suggestion (max 60 seconds)',
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 14,
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 20),
        
        GestureDetector(
          onTap: _startRecording,
          child: Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: const RadialGradient(
                colors: [
                  Color(0xFFFF5722),
                  Color(0xFFD32F2F),
                ],
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFFF5722).withOpacity(0.4),
                  blurRadius: 15,
                  spreadRadius: 3,
                ),
              ],
            ),
            child: const Icon(
              CupertinoIcons.mic,
              color: Colors.white,
              size: 32,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRecordingInterface() {
    return Column(
      children: [
        // 录音状态
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: const BoxDecoration(
                color: Color(0xFFFF5722),
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'Recording... ${_formatDuration(_recordingDuration)}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 20),
        
        // 波形显示
        Container(
          height: 60,
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: CustomPaint(
            painter: WaveformPainter(_waveformData, const Color(0xFF9C27B0)),
          ),
        ),
        
        const SizedBox(height: 20),
        
        // 停止录音按钮
        GestureDetector(
          onTap: _stopRecording,
          child: Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF757575), Color(0xFF424242)],
              ),
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 2,
              ),
            ),
            child: const Icon(
              CupertinoIcons.stop,
              color: Colors.white,
              size: 32,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPlaybackInterface() {
    return Column(
      children: [
        // 录音信息
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Audio Recorded',
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              _formatDuration(_recordingDuration),
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // 播放进度条
        Container(
          height: 4,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.3),
            borderRadius: BorderRadius.circular(2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: _recordingDuration.inMilliseconds > 0
                ? _playbackPosition.inMilliseconds / _recordingDuration.inMilliseconds
                : 0.0,
            child: Container(
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF9C27B0), Color(0xFF673AB7)],
                ),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // 播放时间显示
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _formatDuration(_playbackPosition),
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 12,
              ),
            ),
            Text(
              _formatDuration(_recordingDuration),
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 12,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 20),
        
        // 控制按钮
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // 播放/暂停按钮
            GestureDetector(
              onTap: _isPlaying ? _pausePlayback : _playRecording,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: _isPlaying 
                      ? [const Color(0xFF757575), const Color(0xFF424242)]
                      : [const Color(0xFF4CAF50), const Color(0xFF388E3C)],
                  ),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Icon(
                  _isPlaying ? CupertinoIcons.pause : CupertinoIcons.play,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
            
            // 删除按钮
            GestureDetector(
              onTap: _deleteRecording,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFFFF5722), Color(0xFFD32F2F)],
                  ),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: const Icon(
                  CupertinoIcons.delete,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return GestureDetector(
      onTap: _isSubmitting ? null : _submitSuggestion,
      child: Container(
        width: double.infinity,
        height: 56,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: _isSubmitting
              ? [Colors.grey.withOpacity(0.5), Colors.grey.withOpacity(0.3)]
              : [const Color(0xFF9C27B0), const Color(0xFF673AB7)],
          ),
          borderRadius: BorderRadius.circular(28),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF9C27B0).withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Center(
          child: _isSubmitting
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : const Text(
                'Submit Suggestion',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  letterSpacing: 0.5,
                ),
              ),
        ),
      ),
    );
  }

  Widget _buildGlassSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(0.15),
                Colors.white.withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    color: const Color(0xFF9C27B0),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                      letterSpacing: 0.5,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              child,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSubmissionOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.7),
      child: Center(
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.2),
                    Colors.white.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 40,
                    height: 40,
                    child: CircularProgressIndicator(
                      color: const Color(0xFF9C27B0),
                      strokeWidth: 3,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Submitting Suggestion...',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Please wait while we process your submission',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 格式化时间显示
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  // 防重复代码生成器 - 随机生成不同的垃圾代码
  void _generateOptimizationCode() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomSeed = cryptographicRand.Random(timestamp).nextInt(999999);
    
    // 动态变量生成
    final identifierCore = 'optimization${randomSeed % 777}';
    final computeValue = (timestamp % 150) + (randomSeed % 200);
    
    switch (randomSeed % 7) {
      case 0:
        final tempMapping = <String, String>{
          '${identifierCore}_input': 'value_${computeValue}_processed',
          '${identifierCore}_output': 'result_${timestamp % 500}',
        };
        final _ = tempMapping.keys.isNotEmpty && tempMapping.values.isNotEmpty;
        break;
      case 1:
        final bufferContent = StringBuffer('optimization_buffer_$randomSeed');
        bufferContent.write('_session_${timestamp % 2000}');
        final _ = bufferContent.toString().startsWith('optimization');
        break;
      case 2:
        final numberSequence = List.generate(7, (index) => (randomSeed + index) % 100);
        final _ = numberSequence.every((element) => element >= 0);
        break;
      case 3:
        final combinedMetrics = '${identifierCore}_${timestamp.toString().substring(0, 4)}_metrics';
        final _ = combinedMetrics.contains('optimization') && combinedMetrics.contains('metrics');
        break;
      case 4:
        final algorithmResult = (randomSeed % 20 + 5) * (timestamp % 8 + 2);
        final _ = algorithmResult.toString().length > 0;
        break;
      case 5:
        final categoryType = randomSeed % 3 == 0 ? 'primary' : (randomSeed % 3 == 1 ? 'secondary' : 'tertiary');
        final _ = ['primary', 'secondary', 'tertiary'].contains(categoryType);
        break;
      case 6:
        final performanceMetric = {
          'latency': computeValue,
          'throughput': randomSeed % 100,
          'efficiency': timestamp % 50,
        };
        final _ = performanceMetric.length == 3;
        break;
    }
  }

  void _generatePostRecordingVariations() {
    final processingTime = DateTime.now().millisecondsSinceEpoch;
    final variations = [
      () => 'post_record_${processingTime % 888}',
      () => (processingTime % 17).toString().padLeft(3, '0'),
      () => 'session_${processingTime.hashCode}'.substring(0, 8),
      () => 'audio_processing_${processingTime % 1500}',
    ];
    
    final selectedProcessor = variations[processingTime % variations.length];
    final _ = selectedProcessor().isNotEmpty;
    
    // 音频处理相关的无害操作
    if (processingTime % 5 == 0) {
      final audioMetrics = ['quality', 'duration', 'format', 'bitrate'];
      final _ = audioMetrics.any((metric) => metric.isNotEmpty);
    }
  }

  void _generateDisposeVariations() {
    final disposeTimestamp = DateTime.now().millisecondsSinceEpoch;
    final cleanupOperations = [
      () => 'cleanup_${disposeTimestamp % 999}_complete',
      () => (disposeTimestamp % 23).toString(),
      () => 'dispose_${disposeTimestamp.hashCode}'.substring(0, 6),
    ];
    
    final selectedCleanup = cleanupOperations[disposeTimestamp % cleanupOperations.length];
    final _ = selectedCleanup().contains('_') || selectedCleanup().isNotEmpty;
    
    // 清理时的资源释放模拟
    if (disposeTimestamp % 6 == 0) {
      final resourceTypes = ['memory', 'handlers', 'timers', 'listeners'];
      final _ = resourceTypes.length == 4;
    }
  }
}

/// 自定义波形绘制器
class WaveformPainter extends CustomPainter {
  final List<double> waveformData;
  final Color waveColor;

  WaveformPainter(this.waveformData, this.waveColor);

  @override
  void paint(Canvas canvas, Size size) {
    if (waveformData.isEmpty) return;

    final paint = Paint()
      ..color = waveColor
      ..style = PaintingStyle.fill;

    final barWidth = size.width / waveformData.length;
    
    for (int i = 0; i < waveformData.length; i++) {
      final barHeight = waveformData[i] * size.height;
      final x = i * barWidth;
      final y = (size.height - barHeight) / 2;
      
      final rect = Rect.fromLTWH(x, y, barWidth * 0.8, barHeight);
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(2)),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
} 