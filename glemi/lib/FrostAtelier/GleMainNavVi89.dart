import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'GleMainVi78.dart';
import 'GleChatHistoryVi67.dart';
import 'GleFavoriteVi89.dart';
import 'GleProfileVi33.dart';

/// Glemi主导航页面
/// 底部导航栏集成所有主要功能页面
class GleMainNavVi89 extends StatefulWidget {
  const GleMainNavVi89({super.key});

  @override
  State<GleMainNavVi89> createState() => _GleMainNavVi89State();
}

class _GleMainNavVi89State extends State<GleMainNavVi89>
    with TickerProviderStateMixin {
  
  int _currentIndex = 0;
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // 导航页面列表
  final List<Widget> _pages = [
    const GleMainVi78(),      // 主页
    const GleChatHistoryVi67(), // 聊天历史
    const GleFavoriteVi89(),   // 收藏页面
    const GleProfileVi33(),    // 个人中心
  ];

  // 导航项配置
  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      icon: CupertinoIcons.paintbrush,
      activeIcon: CupertinoIcons.paintbrush_fill,
      label: 'Coaches',
      color: const Color(0xFF87CEEB),
    ),
    NavigationItem(
      icon: CupertinoIcons.chat_bubble_2,
      activeIcon: CupertinoIcons.chat_bubble_2_fill,
      label: 'Chats',
      color: const Color(0xFFB0E0E6),
    ),
    NavigationItem(
      icon: CupertinoIcons.heart,
      activeIcon: CupertinoIcons.heart_fill,
      label: 'Favorites',
      color: const Color(0xFFFF69B4),
    ),
    NavigationItem(
      icon: CupertinoIcons.person,
      activeIcon: CupertinoIcons.person_fill,
      label: 'Profile',
      color: const Color(0xFFFFD700),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    if (index != _currentIndex) {
      setState(() {
        _currentIndex = index;
      });
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        children: _pages,
      ),
      bottomNavigationBar: FadeTransition(
        opacity: _fadeAnimation,
        child: _buildCustomBottomNavBar(),
      ),
    );
  }

  /// 构建自定义底部导航栏
  Widget _buildCustomBottomNavBar() {
    return Container(
      margin: const EdgeInsets.only(
        left: 16,
        right: 16,
        bottom: 32,
      ),
      height: 70,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withOpacity(0.25),
                  Colors.white.withOpacity(0.1),
                  Colors.black.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(_navigationItems.length, (index) {
                return _buildNavItem(index);
              }),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建导航项
  Widget _buildNavItem(int index) {
    final item = _navigationItems[index];
    final isActive = _currentIndex == index;
    
    return GestureDetector(
      onTap: () => _onItemTapped(index),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.elasticOut,
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 6,
        ),
        decoration: BoxDecoration(
          gradient: isActive
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  item.color.withOpacity(0.3),
                  item.color.withOpacity(0.1),
                ],
              )
            : null,
          borderRadius: BorderRadius.circular(18),
          border: isActive
            ? Border.all(
                color: item.color.withOpacity(0.4),
                width: 1,
              )
            : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 图标容器
            Container(
              width: 26,
              height: 26,
              decoration: BoxDecoration(
                color: isActive 
                  ? item.color.withOpacity(0.2)
                  : Colors.transparent,
                shape: BoxShape.circle,
              ),
              child: Icon(
                isActive ? item.activeIcon : item.icon,
                color: isActive 
                  ? item.color
                  : Colors.white.withOpacity(0.6),
                size: 16,
              ),
            ),
            
            const SizedBox(height: 2),
            
            // 标签文字
            Text(
              item.label,
              style: TextStyle(
                fontSize: isActive ? 10 : 9,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                color: isActive 
                  ? item.color
                  : Colors.white.withOpacity(0.6),
                letterSpacing: 0.3,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 导航项配置类
class NavigationItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final Color color;

  NavigationItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    required this.color,
  });
} 