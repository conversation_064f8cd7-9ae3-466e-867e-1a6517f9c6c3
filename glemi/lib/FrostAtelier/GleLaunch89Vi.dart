import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'GleMainNavVi89.dart';
import '../CulletParts/GleArtisticBg67.dart';

/// Glemi玻璃画创作指导师 - 艺术感启动页面
/// 融合玻璃画艺术元素，打造独特视觉体验
class GleLaunch89Vi extends StatefulWidget {
  const GleLaunch89Vi({super.key});

  @override
  State<GleLaunch89Vi> createState() => _GleLaunch89ViState();
}

class _GleLaunch89ViState extends State<GleLaunch89Vi>
    with TickerProviderStateMixin {
  
  // 多重动画控制器
  late AnimationController _anim67Ctrl;
  late AnimationController _pulse34Ctrl;
  
  late Animation<double> _fade88Anim;
  late Animation<double> _scale45Anim;
  late Animation<Offset> _slide23Anim;
  late Animation<double> _pulseAnim;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startWelc78omeSeq();
  }

  /// 初始化动画
  void _initAnimations() {
    // 主动画控制器
    _anim67Ctrl = AnimationController(
      duration: const Duration(milliseconds: 2200),
      vsync: this,
    );

    // 脉冲动画控制器
    _pulse34Ctrl = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // 淡入动画
    _fade88Anim = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _anim67Ctrl,
      curve: const Interval(0.0, 0.7, curve: Curves.easeOut),
    ));

    // 缩放动画
    _scale45Anim = Tween<double>(
      begin: 0.6,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _anim67Ctrl,
      curve: const Interval(0.2, 0.9, curve: Curves.elasticOut),
    ));

    // 滑动动画
    _slide23Anim = Tween<Offset>(
      begin: const Offset(0, 0.4),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _anim67Ctrl,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOutCubic),
    ));

    // 脉冲动画
    _pulseAnim = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulse34Ctrl,
      curve: Curves.easeInOut,
    ));
  }

  /// 启动欢迎动画序列
  void _startWelc78omeSeq() {
    Future.delayed(const Duration(milliseconds: 400), () {
      if (mounted) {
        _anim67Ctrl.forward();
        _pulse34Ctrl.repeat(reverse: true);
      }
    });
  }

  /// 启动按钮点击事件 - 导航到主页
  void _onLaunch92Tap() {
    print('🎨 Launching Glemi Glass Painting Guide...');
    
    // 停止动画
    _pulse34Ctrl.stop();
    
    // 按钮点击动画反馈
    _anim67Ctrl.reverse().then((_) {
      if (mounted) {
        // 导航到主导航页面（包含底部导航栏）
        Navigator.of(context).pushReplacement(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => const GleMainNavVi89(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.easeInOut;
              
              var tween = Tween(begin: begin, end: end).chain(
                CurveTween(curve: curve),
              );
              
              return SlideTransition(
                position: animation.drive(tween),
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 800),
          ),
        );
      }
    });
  }

  @override
  void dispose() {
    _anim67Ctrl.dispose();
    _pulse34Ctrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // 背景组件 - 全屏显示，不受SafeArea影响
          Positioned.fill(
            child: GleArtisticBg67(
              primaryColor: const Color(0xFF9C27B0),
              secondaryColor: const Color(0xFF673AB7),
              accentColor: const Color(0xFFE1BEE7),
              includeFloatingElements: true,
              backgroundImage: 'assets/bg2.png',
              backgroundFit: BoxFit.fill, // 确保在iPad上正确显示
            ),
          ),

          // 内容区域 - 使用SafeArea保护内容不被状态栏遮挡
          SafeArea(
            child: AnimatedBuilder(
              animation: Listenable.merge([_anim67Ctrl, _pulse34Ctrl]),
              builder: (context, child) {
                return Column(
                  children: [
                    // 艺术化顶部区域
                    // _buildArtistic78Header(),

                    // 主要内容区域
                    Expanded(
                      child: _buildMain67Content(),
                    ),

                    // 底部启动按钮区域
                    _buildLaunch23Bottom(),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建艺术化顶部区域 - 简化为透明区域
  Widget _buildArtistic78Header() {
    return FadeTransition(
      opacity: _fade88Anim,
      child: Container(
        // 保持状态栏覆盖高度
        height: 100 + MediaQuery.of(context).padding.top,
        width: double.infinity,
        decoration: const BoxDecoration(
          color: Colors.transparent, // 透明背景，使用艺术背景组件
        ),
        child: Center(
          child: Padding(
            padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
            child: const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Welcome to',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white,
                    letterSpacing: 1.5,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                SizedBox(height: 6),
                Text(
                  'GLEMI',
                  style: TextStyle(
                    fontSize: 30,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 3.0,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建主要内容区域
  Widget _buildMain67Content() {
    return SlideTransition(
      position: _slide23Anim,
      child: FadeTransition(
        opacity: _fade88Anim,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 艺术化Logo区域
              _buildArtistic89Logo(),
              
              const SizedBox(height: 40),
              
              // 创意标题
              _buildCreative34Title(),
              
              const SizedBox(height: 50), // 增加间距，为按钮上移留出空间
              
              // 启动按钮上移至内容区域
              _buildArtistic67Button(),
              
              // 移除描述区域和功能卡片以解决溢出问题
            ],
          ),
        ),
      ),
    );
  }

  /// 构建玻璃拟态Logo区域
  Widget _buildArtistic89Logo() {
    return ScaleTransition(
      scale: _scale45Anim,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(80),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            width: 160,
            height: 160,
            // decoration: BoxDecoration(
            //   gradient: LinearGradient(
            //     begin: Alignment.topLeft,
            //     end: Alignment.bottomRight,
            //     colors: [
            //       Colors.white.withOpacity(0.2),
            //       Colors.white.withOpacity(0.1),
            //       const Color(0xFF9C27B0).withOpacity(0.1),
            //     ],
            //   ),
            //   borderRadius: BorderRadius.circular(80),
            //   border: Border.all(
            //     color: Colors.white.withOpacity(0.3),
            //     width: 2,
            //   ),
            //   boxShadow: [
            //     BoxShadow(
            //       color: const Color(0xFF9C27B0).withOpacity(0.3),
            //       blurRadius: 30,
            //       spreadRadius: 5,
            //     ),
            //   ],
            // ),
            child: Stack(
              children: [
                // 主玻璃画图标
                Center(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(25),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                      child: Container(
                        width: 90,
                        height: 90,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              const Color(0xFF9C27B0).withOpacity(0.3),
                              const Color(0xFF673AB7).withOpacity(0.2),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(25),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        child: const Icon(
                          CupertinoIcons.paintbrush_fill,
                          size: 45,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
            
                // 装饰小点 - 玻璃拟态风格
                Positioned(
                  top: 30,
                  right: 35,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE1BEE7).withOpacity(0.8),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFFE1BEE7).withOpacity(0.4),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                  ),
                ),

                Positioned(
                  bottom: 40,
                  left: 30,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: const Color(0xFF9C27B0).withOpacity(0.8),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF9C27B0).withOpacity(0.4),
                          blurRadius: 6,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                  ),
                ),
            
                Positioned(
                  top: 50,
                  left: 25,
                  child: Container(
                    width: 10,
                    height: 10,
                    decoration: BoxDecoration(
                      color: const Color(0xFF673AB7).withOpacity(0.8),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF673AB7).withOpacity(0.4),
                          blurRadius: 5,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建创意标题 - 玻璃画风格重新设计
  Widget _buildCreative34Title() {
    return Column(
      children: [
        // Glemi主标题 - 玻璃拟态设计
        ClipRRect(
          borderRadius: BorderRadius.circular(25),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(
              // decoration: BoxDecoration(
              //   gradient: LinearGradient(
              //     begin: Alignment.topLeft,
              //     end: Alignment.bottomRight,
              //     colors: [
              //       Colors.white.withOpacity(0.2),
              //       Colors.white.withOpacity(0.1),
              //     ],
              //   ),
              //   borderRadius: BorderRadius.circular(25),
              //   border: Border.all(
              //     color: Colors.white.withOpacity(0.3),
              //     width: 1,
              //   ),
              //   boxShadow: [
              //     BoxShadow(
              //       color: const Color(0xFF9C27B0).withOpacity(0.3),
              //       blurRadius: 20,
              //       offset: const Offset(0, 8),
              //     ),
              //   ],
              // ),
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              child: ShaderMask(
                shaderCallback: (bounds) => const LinearGradient(
                  colors: [
                    Color(0xFF9C27B0),
                    Color(0xFF673AB7),
                    Color(0xFFE1BEE7),
                    Color(0xFF9C27B0),
                  ],
                ).createShader(bounds),
            child: const Text(
              'Glemi',
              style: TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.w800,
                color: Colors.white,
                letterSpacing: 4.0,
                height: 1.0,
              ),
                ),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 20),
        
        // 副标题 - 玻璃拟态风格
        ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              // decoration: BoxDecoration(
              //   gradient: LinearGradient(
              //     begin: Alignment.topLeft,
              //     end: Alignment.bottomRight,
              //     colors: [
              //       Colors.white.withOpacity(0.15),
              //       Colors.white.withOpacity(0.05),
              //     ],
              //   ),
              //   borderRadius: BorderRadius.circular(20),
              //   border: Border.all(
              //     color: Colors.white.withOpacity(0.2),
              //     width: 1,
              //   ),
              // ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
              child: Text(
                'AI Glass Painting Studio',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.9),
                  letterSpacing: 1.8,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建底部启动按钮区域 - 简化为空
  Widget _buildLaunch23Bottom() {
    return const SizedBox.shrink(); // 空组件，因为按钮已移至主内容区域
  }

  /// 构建玻璃拟态启动按钮
  Widget _buildArtistic67Button() {
    return AnimatedBuilder(
      animation: _pulseAnim,
      builder: (context, child) {
        return Transform.scale(
          scale: _scale45Anim.value * _pulseAnim.value,
          child: GestureDetector(
            onTap: _onLaunch92Tap,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(35),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                child: Container(
                  width: double.infinity,
                  height: 70,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withOpacity(0.2),
                        Colors.white.withOpacity(0.1),
                        const Color(0xFF9C27B0).withOpacity(0.1 * _pulseAnim.value),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(35),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3 + 0.2 * (_pulseAnim.value - 1).abs()),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF9C27B0).withOpacity(0.3 * _pulseAnim.value),
                        blurRadius: 20 + 10 * (_pulseAnim.value - 1).abs(),
                        offset: const Offset(0, 8),
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // 左侧装饰圆点
                      Container(
                        width: 12 + 4 * (_pulseAnim.value - 1).abs(),
                        height: 12 + 4 * (_pulseAnim.value - 1).abs(),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE1BEE7).withOpacity(0.8),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFFE1BEE7).withOpacity(0.6 * _pulseAnim.value),
                              blurRadius: 8 + 6 * (_pulseAnim.value - 1).abs(),
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(width: 20),

                      // 按钮文字
                      ShaderMask(
                        shaderCallback: (bounds) => LinearGradient(
                          colors: [
                            Color.lerp(const Color(0xFF9C27B0), const Color(0xFF673AB7), (_pulseAnim.value - 1).abs())!,
                            const Color(0xFFE1BEE7),
                            Color.lerp(const Color(0xFF673AB7), const Color(0xFF9C27B0), (_pulseAnim.value - 1).abs())!,
                          ],
                        ).createShader(bounds),
                        child: const Text(
                          'Enter Studio',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                            letterSpacing: 2.0,
                          ),
                        ),
                      ),

                      const SizedBox(width: 20),

                      // 右侧装饰圆点
                      Container(
                        width: 12 + 4 * (_pulseAnim.value - 1).abs(),
                        height: 12 + 4 * (_pulseAnim.value - 1).abs(),
                        decoration: BoxDecoration(
                          color: const Color(0xFF9C27B0).withOpacity(0.8),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF9C27B0).withOpacity(0.6 * _pulseAnim.value),
                              blurRadius: 8 + 6 * (_pulseAnim.value - 1).abs(),
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// 三角形画笔
class TrianglePainter extends CustomPainter {
  final Color color;
  
  TrianglePainter(this.color);
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = color;
    final path = Path();
    path.moveTo(size.width / 2, 0);
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.close();
    canvas.drawPath(path, paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 