import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'package:intl/intl.dart';
import '../CulletParts/GleArtisticBg67.dart';
import '../PrismVault/GleChatData89.dart';
import '../FrostAtelier/GleMainVi78.dart';
import 'GleChatVi45.dart';

/// Glemi聊天历史管理页面
/// 高级UI设计，显示所有聊天会话
class GleChatHistoryVi67 extends StatefulWidget {
  const GleChatHistoryVi67({super.key});

  @override
  State<GleChatHistoryVi67> createState() => _GleChatHistoryVi67State();
}

class _GleChatHistoryVi67State extends State<GleChatHistoryVi67>
    with TickerProviderStateMixin {
  
  late AnimationController _fadeCtrl;
  late AnimationController _scaleCtrl;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  List<ChatSession> _chatSessions = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _loadChatSessions();
    _startAnimations();
  }

  void _initAnimations() {
    _fadeCtrl = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _scaleCtrl = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeCtrl,
      curve: Curves.easeOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleCtrl,
      curve: Curves.elasticOut,
    ));
  }

  void _startAnimations() {
    _fadeCtrl.forward();
    _scaleCtrl.forward();
  }

  /// 加载聊天会话
  Future<void> _loadChatSessions() async {
    setState(() {
      _isLoading = true;
    });
    
    final sessions = await GleChatDataManager.getAllChatSessions();
    
    setState(() {
      _chatSessions = sessions;
      _isLoading = false;
    });
  }

  /// 删除聊天会话
  Future<void> _deleteChatSession(ChatSession session) async {
    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.5),
      builder: (context) => BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Dialog(
          backgroundColor: Colors.transparent,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(25),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withOpacity(0.2),
                      Colors.white.withOpacity(0.1),
                    ],
                  ),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      CupertinoIcons.delete,
                      color: const Color(0xFFFF6B6B),
                      size: 48,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    Text(
                      'Delete Chat History',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    Text(
                      'Are you sure you want to delete your chat history with ${session.coachName}? This action cannot be undone.',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white.withOpacity(0.8),
                        height: 1.4,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    
                    const SizedBox(height: 24),
                    
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: Text(
                              'Cancel',
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.7),
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFFFF6B6B), Color(0xFFFF8E8E)],
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: TextButton(
                              onPressed: () async {
                                await GleChatDataManager.deleteChatSession(session.coachId);
                                Navigator.pop(context);
                                _loadChatSessions();
                                
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Chat history deleted'),
                                    backgroundColor: const Color(0xFF87CEEB),
                                    behavior: SnackBarBehavior.floating,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                  ),
                                );
                              },
                              child: const Text(
                                'Delete',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 进入聊天页面
  void _enterChat(ChatSession session) {
    // 创建AiCoach模型
    final coach = _findCoachById(session.coachId);
    if (coach != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => GleChatVi45(coach: coach),
        ),
      ).then((_) {
        // 从聊天页面返回后重新加载数据
        _loadChatSessions();
      });
    }
  }

  /// 根据ID查找教练信息
  AiCoach34Model? _findCoachById(String coachId) {
    // 这里使用主页的教练数据
    final coaches = [
      AiCoach34Model(
        id: 1,
        name: 'BasicLineOutliner',
        displayName: 'Line Master',
        description: 'Master fundamental line drawing techniques for glass painting',
        specialty: 'Basic Lines',
        imagePath: 'assets/gl/gl_1.png',
        glassColor: const Color(0xFF20B2AA),
        accentColor: const Color(0xFF87CEEB),
      ),
      AiCoach34Model(
        id: 2,
        name: 'ColorGradientMixer',
        displayName: 'Color Wizard',
        description: 'Create smooth color gradients and blending techniques',
        specialty: 'Color Blending',
        imagePath: 'assets/gl/gl_2.png',
        glassColor: const Color(0xFF4682B4),
        accentColor: const Color(0xFFB0E0E6),
      ),
      AiCoach34Model(
        id: 3,
        name: 'OutlineAccentCoach',
        displayName: 'Edge Artist',
        description: 'Perfect outlining techniques to define painting details',
        specialty: 'Outline Design',
        imagePath: 'assets/gl/gl_3.png',
        glassColor: const Color(0xFF2E8B57),
        accentColor: const Color(0xFF98FB98),
      ),
      AiCoach34Model(
        id: 4,
        name: 'GlassTextureIllustrator',
        displayName: 'Texture Pro',
        description: 'Depict realistic glass texture and sheen effects',
        specialty: 'Glass Texture',
        imagePath: 'assets/gl/gl_4.png',
        glassColor: const Color(0xFF6495ED),
        accentColor: const Color(0xFFE6E6FA),
      ),
      AiCoach34Model(
        id: 5,
        name: 'MiniPatternCreator',
        displayName: 'Pattern Genius',
        description: 'Design intricate small patterns for glassware',
        specialty: 'Mini Patterns',
        imagePath: 'assets/gl/gl_5.png',
        glassColor: const Color(0xFF40E0D0),
        accentColor: const Color(0xFFAFEEEE),
      ),
      AiCoach34Model(
        id: 6,
        name: 'ThemeSceneConstructor',
        displayName: 'Scene Builder',
        description: 'Build cohesive themed scenes in glass painting',
        specialty: 'Scene Design',
        imagePath: 'assets/gl/gl_6.png',
        glassColor: const Color(0xFF5F9EA0),
        accentColor: const Color(0xFFB0C4DE),
      ),
      AiCoach34Model(
        id: 7,
        name: 'PaintPropertyUtilizer',
        displayName: 'Paint Expert',
        description: 'Master glass paint properties for unique effects',
        specialty: 'Paint Effects',
        imagePath: 'assets/gl/gl_7.png',
        glassColor: const Color(0xFF708090),
        accentColor: const Color(0xFFD3D3D3),
      ),
      AiCoach34Model(
        id: 8,
        name: 'DecorativeBorderInstructor',
        displayName: 'Border Master',
        description: 'Create beautiful decorative borders for glass items',
        specialty: 'Border Design',
        imagePath: 'assets/gl/gl_8.png',
        glassColor: const Color(0xFF4169E1),
        accentColor: const Color(0xFFDDA0DD),
      ),
      AiCoach34Model(
        id: 9,
        name: 'MistakeCorrectionGuide',
        displayName: 'Fix Specialist',
        description: 'Fix common glass painting mistakes with ease',
        specialty: 'Error Correction',
        imagePath: 'assets/gl/gl_9.png',
        glassColor: const Color(0xFF32CD32),
        accentColor: const Color(0xFF90EE90),
      ),
      AiCoach34Model(
        id: 10,
        name: 'CuringAndProtectionAdvisor',
        displayName: 'Care Expert',
        description: 'Proper curing and protection for lasting artworks',
        specialty: 'Care & Protection',
        imagePath: 'assets/gl/gl_10.png',
        glassColor: const Color(0xFF8A2BE2),
        accentColor: const Color(0xFFDDA0DD),
      ),
    ];

    try {
      return coaches.firstWhere((coach) => coach.name == coachId);
    } catch (e) {
      return null;
    }
  }

  @override
  void dispose() {
    _fadeCtrl.dispose();
    _scaleCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildGlassAppBar(),
      body: GleArtisticBg67(
        primaryColor: const Color(0xFF87CEEB),
        secondaryColor: const Color(0xFFB0E0E6),
        accentColor: const Color(0xFFFFF8DC),
        includeFloatingElements: true,
        backgroundImage: 'assets/bg2.png', // 添加背景图片
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: _isLoading 
                ? _buildLoadingState()
                : _chatSessions.isEmpty
                  ? _buildEmptyState()
                  : _buildChatSessionsList(),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建玻璃拟态AppBar
  PreferredSizeWidget _buildGlassAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(60),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: AppBar(
            backgroundColor: Colors.white.withOpacity(0.1),
            elevation: 0,
            leading: IconButton(
              icon: Icon(
                CupertinoIcons.back,
                color: Colors.white.withOpacity(0.9),
                size: 24,
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
            title: Text(
              'Chat History',
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            centerTitle: true,
            actions: [
              IconButton(
                icon: Icon(
                  CupertinoIcons.refresh,
                  color: Colors.white.withOpacity(0.9),
                  size: 24,
                ),
                onPressed: _loadChatSessions,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Center(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withOpacity(0.15),
                  Colors.white.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 40,
                  height: 40,
                  child: CircularProgressIndicator(
                    color: Colors.white.withOpacity(0.8),
                    strokeWidth: 3,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Loading chat history...',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(25),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.15),
                    Colors.white.withOpacity(0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    CupertinoIcons.chat_bubble_2,
                    color: Colors.white.withOpacity(0.6),
                    size: 64,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'No Chat History Yet',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Start chatting with AI coaches to see your conversation history here.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withOpacity(0.7),
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  Container(
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF87CEEB), Color(0xFFB0E0E6)],
                      ),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text(
                        'Start Chatting',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建聊天会话列表
  Widget _buildChatSessionsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      physics: const BouncingScrollPhysics(),
      itemCount: _chatSessions.length,
      itemBuilder: (context, index) {
        final session = _chatSessions[index];
        return _buildChatSessionCard(session, index);
      },
    );
  }

  /// 构建聊天会话卡片
  Widget _buildChatSessionCard(ChatSession session, int index) {
    final lastMessage = session.messages.isNotEmpty 
      ? session.messages.last
      : null;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: TweenAnimationBuilder<double>(
        duration: Duration(milliseconds: 800 + index * 100),
        tween: Tween<double>(begin: 0.0, end: 1.0),
        builder: (context, value, child) {
          return Transform.translate(
            offset: Offset(0, 30 * (1 - value)),
            child: Opacity(
              opacity: value,
              child: child,
            ),
          );
        },
        child: GestureDetector(
          onTap: () => _enterChat(session),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withOpacity(0.15),
                      Colors.white.withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    // AI教练头像
                    Hero(
                      tag: 'coach_avatar_${session.coachId}',
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              const Color(0xFF87CEEB).withOpacity(0.4),
                              const Color(0xFFB0E0E6).withOpacity(0.2),
                              Colors.transparent,
                            ],
                            stops: const [0.0, 0.7, 1.0],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF87CEEB).withOpacity(0.3),
                              blurRadius: 15,
                              spreadRadius: 3,
                            ),
                          ],
                        ),
                        child: Container(
                          margin: const EdgeInsets.all(3),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white.withOpacity(0.4),
                              width: 2,
                            ),
                          ),
                          child: ClipOval(
                            child: Image.asset(
                              session.coachImagePath,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color: const Color(0xFF87CEEB),
                                  child: Icon(
                                    CupertinoIcons.person_circle,
                                    color: Colors.white,
                                    size: 32,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    // 聊天信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 教练名称
                          Text(
                            session.coachName,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w700,
                              color: Colors.white,
                            ),
                          ),
                          
                          const SizedBox(height: 4),
                          
                          // 最后一条消息
                          if (lastMessage != null) ...[
                            Text(
                              lastMessage.isUser
                                ? 'You: ${lastMessage.content}'
                                : lastMessage.content,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white.withOpacity(0.7),
                                fontWeight: FontWeight.w400,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8),
                            
                            // 时间和消息数量
                            Row(
                              children: [
                                Icon(
                                  CupertinoIcons.time,
                                  color: Colors.white.withOpacity(0.5),
                                  size: 12,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  DateFormat('MMM d, HH:mm').format(session.lastUpdated),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.white.withOpacity(0.5),
                                  ),
                                ),
                                const Spacer(),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF87CEEB).withOpacity(0.3),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    '${session.messages.length} messages',
                                    style: const TextStyle(
                                      fontSize: 11,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ] else ...[
                            Text(
                              'No messages yet',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white.withOpacity(0.5),
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    
                    // 操作按钮
                    Column(
                      children: [
                        // 进入聊天按钮
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFF87CEEB), Color(0xFFB0E0E6)],
                            ),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white.withOpacity(0.4),
                              width: 1,
                            ),
                          ),
                          child: Icon(
                            CupertinoIcons.chevron_right,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                        
                        const SizedBox(height: 8),
                        
                        // 删除按钮
                        GestureDetector(
                          onTap: () => _deleteChatSession(session),
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Icon(
                              CupertinoIcons.delete,
                              color: const Color(0xFFFF6B6B),
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
} 