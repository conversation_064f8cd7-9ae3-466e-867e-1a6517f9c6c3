import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'package:shared_preferences/shared_preferences.dart';
import '../CulletParts/GleArtisticBg67.dart';
import '../PrismVault/GleCoinDataMgr45.dart';
import 'GleChatHistoryVi67.dart';
import 'GleCoinShopVi23.dart';
import 'GleAdviceVi78.dart';
import 'GlePrivacyVi56.dart';

/// Glemi个人中心页面
/// 包含用户信息展示和各种功能选项
class GleProfileVi33 extends StatefulWidget {
  const GleProfileVi33({super.key});

  @override
  State<GleProfileVi33> createState() => _GleProfileVi33State();
}

class _GleProfileVi33State extends State<GleProfileVi33>
    with TickerProviderStateMixin {
  
  late AnimationController _fadeCtrl;
  late AnimationController _scaleCtrl;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  // 用户数据
  String _nickname = 'Glass Artist';
  String _selectedAvatar = 'assets/avatars/avatar_01.jpg';
  int _currentCoinBalance = 0; // 添加金币余额状态
  
  // 可用头像列表
  final List<String> _availableAvatars = [
    'assets/avatars/avatar_01.jpg',
    'assets/avatars/avatar_02.jpg',
    'assets/avatars/avatar_03.jpg',
    'assets/avatars/avatar_04.jpg',
    'assets/avatars/avatar_05.jpg',
    'assets/avatars/avatar_06.jpg',
  ];
  
  // 玻璃画主题昵称建议
  final List<String> _glassPaintingNicknames = [
    'Glass Artist',
    'Crystal Creator',
    'Stained Glass Master',
    'Glass Wizard',
    'Prism Painter',
    'Glassy Soul',
    'Art Glass Lover',
    'Fusion Artist',
    'Glass Dreamer',
    'Crystal Artisan',
  ];

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _loadUserData();
    _initializeCoinManager(); // 初始化金币管理器
    _startAnimations();
  }

  void _initAnimations() {
    _fadeCtrl = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _scaleCtrl = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeCtrl,
      curve: Curves.easeOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleCtrl,
      curve: Curves.elasticOut,
    ));
  }

  void _startAnimations() {
    _fadeCtrl.forward();
    _scaleCtrl.forward();
  }

  /// 加载用户数据
  Future<void> _loadUserData() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _nickname = prefs.getString('user_nickname') ?? 'Glass Artist';
      _selectedAvatar = prefs.getString('user_avatar') ?? 'assets/avatars/avatar_01.jpg';
      _currentCoinBalance = prefs.getInt('user_coin_balance') ?? 0; // 加载金币余额
    });
  }

  /// 保存用户数据
  Future<void> _saveUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_nickname', _nickname);
    await prefs.setString('user_avatar', _selectedAvatar);
  }

  /// 初始化金币管理器
  Future<void> _initializeCoinManager() async {
    await GleCoinDataMgr45().initialize();
    _currentCoinBalance = GleCoinDataMgr45().currentBalance;
    
    // 监听金币余额变化
    GleCoinDataMgr45().coinBalanceStream.listen((balance) {
      if (mounted) {
        setState(() {
          _currentCoinBalance = balance;
        });
      }
    });
  }

  @override
  void dispose() {
    _fadeCtrl.dispose();
    _scaleCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildGlassAppBar(),
      body: GleArtisticBg67(
        primaryColor: const Color(0xFF87CEEB),
        secondaryColor: const Color(0xFFB0E0E6),
        accentColor: const Color(0xFFFFF8DC),
        includeFloatingElements: true,
        backgroundImage: 'assets/bg2.png', // 添加背景图片
        backgroundFit: BoxFit.fill, // 确保在iPad上正确显示
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    const SizedBox(height: 20),
                    
                    // 用户信息卡片
                    _buildUserInfoCard(),
                    
                    const SizedBox(height: 30),
                    
                    // 功能选项列表
                    _buildOptionsSection(),
                    
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建玻璃拟态AppBar
  PreferredSizeWidget _buildGlassAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(60),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: AppBar(
            backgroundColor: Colors.white.withOpacity(0.1),
            elevation: 0,
            // leading: IconButton(
            //   icon: Icon(
            //     CupertinoIcons.back,
            //     color: Colors.white.withOpacity(0.9),
            //     size: 24,
            //   ),
            //   onPressed: () => Navigator.of(context).pop(),
            // ),
            title: Text(
              'My Profile',
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            centerTitle: true,
          ),
        ),
      ),
    );
  }

  /// 构建用户信息卡片
  Widget _buildUserInfoCard() {
    return _buildGlassCard(
      child: Column(
        children: [
          // 头像和编辑按钮
          Stack(
            children: [
              // 用户头像
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      const Color(0xFF87CEEB).withOpacity(0.4),
                      const Color(0xFFB0E0E6).withOpacity(0.2),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF87CEEB).withOpacity(0.4),
                      blurRadius: 20,
                      spreadRadius: 6,
                    ),
                  ],
                ),
                child: Container(
                  margin: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white.withOpacity(0.4),
                      width: 2,
                    ),
                  ),
                  child: ClipOval(
                    child: Image.asset(
                      _selectedAvatar,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: const Color(0xFF87CEEB),
                          child: Icon(
                            CupertinoIcons.person_circle_fill,
                            size: 50,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
              
              // 编辑按钮
              Positioned(
                bottom: 0,
                right: 0,
                child: GestureDetector(
                  onTap: _showAvatarPicker,
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: const Color(0xFF87CEEB),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF87CEEB).withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(
                      CupertinoIcons.camera,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // 用户昵称和编辑按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  _nickname,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w800,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(width: 8),
              GestureDetector(
                onTap: _showNicknameEditor,
                child: Icon(
                  CupertinoIcons.pencil,
                  color: Colors.white.withOpacity(0.7),
                  size: 20,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Text(
            'Glass Painting Enthusiast',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withOpacity(0.7),
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 16),
          
          // 金币余额显示
          GestureDetector(
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const GleCoinShopVi23()),
            ),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFFFD700).withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    CupertinoIcons.money_dollar,
                    color: Colors.white,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '$_currentCoinBalance Coins',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    CupertinoIcons.chevron_right,
                    color: Colors.white.withOpacity(0.8),
                    size: 14,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建功能选项区域
  Widget _buildOptionsSection() {
    return Column(
      children: [
        // 我的收藏
        // _buildOptionItem(
        //   icon: CupertinoIcons.heart,
        //   title: 'My Favorites',
        //   subtitle: 'Favorite coaches and techniques',
        //   onTap: () => _showComingSoon('My Favorites'),
        // ),
        //
        // const SizedBox(height: 16),
        //
        // // 学习记录
        // _buildOptionItem(
        //   icon: CupertinoIcons.book,
        //   title: 'Learning Progress',
        //   subtitle: 'View your glass painting journey',
        //   onTap: () => _showComingSoon('Learning Progress'),
        // ),
        //
        // const SizedBox(height: 16),
        //
        // // 聊天历史
        // _buildOptionItem(
        //   icon: CupertinoIcons.chat_bubble_text,
        //   title: 'Chat History',
        //   subtitle: 'View your chat history',
        //   onTap: () => Navigator.push(
        //     context,
        //     MaterialPageRoute(builder: (context) => GleChatHistoryVi67()),
        //   ),
        // ),
        //
        // const SizedBox(height: 16),
        
        // 用户反馈
        _buildOptionItem(
          icon: CupertinoIcons.lightbulb,
          title: 'User Suggestions',
          subtitle: 'Share your ideas and improvements',
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const GleAdviceVi78()),
          ),
        ),
        
        // const SizedBox(height: 16),
        //
        // // 设置选项
        // _buildOptionItem(
        //   icon: CupertinoIcons.settings,
        //   title: 'Settings',
        //   subtitle: 'App preferences and notifications',
        //   onTap: () => _showComingSoon('Settings'),
        // ),
        //
        // const SizedBox(height: 16),
        //
        // // 帮助支持
        // _buildOptionItem(
        //   icon: CupertinoIcons.question_circle,
        //   title: 'Help & Support',
        //   subtitle: 'Get help with glass painting',
        //   onTap: () => _showComingSoon('Help & Support'),
        // ),
        
        const SizedBox(height: 16),
        
        // 隐私协议
        _buildOptionItem(
          icon: CupertinoIcons.doc_text,
          title: 'Privacy Policy',
          subtitle: 'View our privacy policy',
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const GlePrivacyVi56()),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // 关于我们
        _buildOptionItem(
          icon: CupertinoIcons.info_circle,
          title: 'About Glemi',
          subtitle: 'Learn more about our app',
          onTap: _showAboutDialog,
        ),
      ],
    );
  }

  /// 构建选项条目
  Widget _buildOptionItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: _buildGlassCard(
        child: Row(
          children: [
            // 图标
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: const Color(0xFF87CEEB).withOpacity(0.2),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Icon(
                icon,
                color: Colors.white.withOpacity(0.9),
                size: 24,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // 文字信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white.withOpacity(0.7),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
            
            // 箭头
            Icon(
              CupertinoIcons.chevron_right,
              color: Colors.white.withOpacity(0.5),
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  /// 显示头像选择器
  void _showAvatarPicker() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            height: 400,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.white.withOpacity(0.2),
                  Colors.white.withOpacity(0.1),
                ],
              ),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                // 顶部拖拽指示器
                Container(
                  margin: const EdgeInsets.only(top: 12),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // 标题
                Text(
                  'Choose Avatar',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                  ),
                ),
                
                const SizedBox(height: 30),
                
                // 头像网格
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 30),
                    child: GridView.builder(
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: 20,
                        mainAxisSpacing: 20,
                        childAspectRatio: 1,
                      ),
                      itemCount: _availableAvatars.length,
                      itemBuilder: (context, index) {
                        final avatar = _availableAvatars[index];
                        final isSelected = avatar == _selectedAvatar;
                        
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedAvatar = avatar;
                            });
                            _saveUserData();
                            Navigator.pop(context);
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: isSelected 
                                  ? const Color(0xFF87CEEB)
                                  : Colors.white.withOpacity(0.3),
                                width: isSelected ? 3 : 2,
                              ),
                              boxShadow: isSelected ? [
                                BoxShadow(
                                  color: const Color(0xFF87CEEB).withOpacity(0.4),
                                  blurRadius: 15,
                                  spreadRadius: 3,
                                ),
                              ] : null,
                            ),
                            child: ClipOval(
                              child: Image.asset(
                                avatar,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    color: const Color(0xFF87CEEB),
                                    child: Icon(
                                      CupertinoIcons.person_circle,
                                      size: 40,
                                      color: Colors.white.withOpacity(0.8),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 显示昵称编辑器
  void _showNicknameEditor() {
    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.5),
      builder: (context) => _NicknameEditDialog(
        initialNickname: _nickname,
        glassPaintingNicknames: _glassPaintingNicknames,
        onSave: (newNickname) {
          setState(() {
            _nickname = newNickname;
          });
          _saveUserData();
        },
      ),
    );
  }

  /// 显示即将推出提示
  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature feature coming soon!'),
        backgroundColor: const Color(0xFF87CEEB),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      ),
    );
  }

  /// 显示关于应用的信息框
  void _showAboutDialog() {
    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.5),
      builder: (context) => BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Dialog(
          backgroundColor: Colors.transparent,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(25),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withOpacity(0.2),
                      Colors.white.withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 应用图标和标题
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xFF87CEEB),
                                const Color(0xFF4682B4),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Icon(
                            CupertinoIcons.paintbrush_fill,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Glemi',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.9),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              Text(
                                'AI Glass Painting Studio',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.7),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // 应用介绍
                    Text(
                      'Welcome to Glemi, your personal AI-powered glass painting companion! 🎨\n\n'
                      '✨ Features:\n'
                      '• 10 specialized AI coaches for different techniques\n'
                      '• Personalized guidance for glass art creation\n'
                      '• Interactive chat with expert mentors\n'
                      '• Daily inspiration and creative quotes\n'
                      '• Beautiful glass-morphism design\n\n'
                      '🎯 Perfect for:\n'
                      '• Beginners learning glass painting\n'
                      '• Artists seeking creative inspiration\n'
                      '• Anyone passionate about glass art\n\n'
                      'Start your glass painting journey today with AI-powered guidance!',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 14,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.left,
                    ),

                    const SizedBox(height: 24),

                    // 版本信息
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Version 1.0.0 • Made with ❤️',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.6),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // 关闭按钮
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF87CEEB),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          'Got it!',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建玻璃拟态卡片组件
  Widget _buildGlassCard({required Widget child}) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(0.15),
                Colors.white.withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: child,
        ),
      ),
    );
  }
}

/// 昵称编辑对话框组件
class _NicknameEditDialog extends StatefulWidget {
  final String initialNickname;
  final List<String> glassPaintingNicknames;
  final Function(String) onSave;

  const _NicknameEditDialog({
    required this.initialNickname,
    required this.glassPaintingNicknames,
    required this.onSave,
  });

  @override
  State<_NicknameEditDialog> createState() => _NicknameEditDialogState();
}

class _NicknameEditDialogState extends State<_NicknameEditDialog> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialNickname);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
      child: Dialog(
        backgroundColor: Colors.transparent,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(25),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 500),
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.2),
                    Colors.white.withOpacity(0.1),
                  ],
                ),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 标题
                    Text(
                      'Edit Nickname',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // 输入框 - 修复颜色对比度
                    Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFF2D3748).withOpacity(0.8), // 深色背景
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(
                          color: const Color(0xFF87CEEB).withOpacity(0.5),
                          width: 1,
                        ),
                      ),
                      child: TextField(
                        controller: _controller,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          filled: false,
                          fillColor: Colors.transparent,
                          contentPadding: const EdgeInsets.all(16),
                          hintText: 'Enter nickname',
                          hintStyle: TextStyle(
                            color: Colors.white.withOpacity(0.6),
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // 昵称建议
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        'Suggestions:',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: widget.glassPaintingNicknames.take(6).map((nickname) {
                        return GestureDetector(
                          onTap: () {
                            _controller.text = nickname;
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: const Color(0xFF87CEEB).withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: const Color(0xFF87CEEB).withOpacity(0.4),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              nickname,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // 按钮
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: Text(
                              'Cancel',
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.7),
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFF87CEEB), Color(0xFFB0E0E6)],
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: TextButton(
                              onPressed: () {
                                final newNickname = _controller.text.isNotEmpty 
                                  ? _controller.text 
                                  : 'Glass Artist';
                                widget.onSave(newNickname);
                                Navigator.pop(context);
                              },
                              child: const Text(
                                'Save',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
} 