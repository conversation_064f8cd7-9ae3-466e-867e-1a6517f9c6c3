import 'package:flutter/material.dart';
import 'FrostAtelier/GleMainNavVi89.dart';

void main() {
  print('🎨 Launching Glemi Glass Painting Guide...');
  runApp(const GlemiApp());
}

/// Glemi玻璃画创作指导师主应用
class GlemiApp extends StatelessWidget {
  const GlemiApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Glemi - Glass Art Masters',
      theme: _buildGlemiTheme(),
      home: const GleMainNavVi89(),
      debugShowCheckedModeBanner: false,
    );
  }

  /// 构建Glemi主题配色
  ThemeData _buildGlemiTheme() {
    return ThemeData(
      // 使用Material 3设计
      useMaterial3: true,
      
      // 主色调配置 - 基于玻璃蓝色
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFFA7C8D9), // 玻璃蓝色
        primary: const Color(0xFFA7C8D9),
        secondary: const Color(0xFFFFD700), // 艺术金色
        surface: const Color(0xFFF9F9F9), // 透明白色
        background: const Color(0xFFF5F5F5), // 灰白色
        tertiary: const Color(0xFFD8C8F6), // 淡紫色
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: const Color(0xFF5C2E91), // 深紫色
      ),
      
      // 扁平化设计 - 无阴影
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0, // 无阴影
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12), // 12px圆角
          ),
        ),
      ),
      
      // 卡片主题 - 扁平化
      cardTheme: const CardTheme(
        elevation: 0, // 无阴影
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16)), // 16px圆角
        ),
      ),
      
      // 应用栏主题
      appBarTheme: const AppBarTheme(
        elevation: 0, // 无阴影
        backgroundColor: Color(0xFFA7C8D9), // 玻璃蓝色
        foregroundColor: Colors.white,
        centerTitle: true,
      ),
      
      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: const Color(0xFFF5F5F5), // 灰白色
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none, // 无边框，扁平化
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFFFFD700), // 艺术金色焦点
            width: 2,
          ),
        ),
      ),
      
      // 文字主题
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          color: Color(0xFF5C2E91), // 深紫色
          fontWeight: FontWeight.bold,
        ),
        headlineMedium: TextStyle(
          color: Color(0xFF5C2E91),
          fontWeight: FontWeight.w600,
        ),
        bodyLarge: TextStyle(
          color: Color(0xFF333333),
        ),
        bodyMedium: TextStyle(
          color: Color(0xFF666666),
        ),
      ),
    );
  }
}
