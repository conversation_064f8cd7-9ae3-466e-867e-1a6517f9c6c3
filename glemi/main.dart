import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'lib/FrostAtelier/GleMainNavVi89.dart';
import 'lib/PrismVault/GleCoinDataMgr45.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化金币管理器
  await GleCoinDataMgr45().initialize();
  
  print('🎨 Launching Glemi Glass Painting Guide...');
  runApp(const GlemiApp());
}

/// Glemi玻璃画创作指导师主应用
class GlemiApp extends StatelessWidget {
  const GlemiApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Glemi - Glass Art Masters',
      theme: _buildGlemiTheme(),
      home: const GleMainNavVi89(),
      debugShowCheckedModeBanner: false,
    );
  }

  /// 构建Glemi主题
  ThemeData _buildGlemiTheme() {
    return ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF87CEEB),
        brightness: Brightness.light,
      ),
      useMaterial3: true,
      fontFamily: 'SF Pro Display',
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.w900,
          letterSpacing: -0.5,
        ),
        titleLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.0,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.1,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
      ),
    );
  }
} 